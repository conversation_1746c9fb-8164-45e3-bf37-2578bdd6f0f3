import * as vscode from "vscode";
import { getUri } from "../utilities/getUri";
import { getNonce } from "../utilities/getNonce";

export class SidebarPanel {
  public static currentPanel: SidebarPanel | undefined;
  public static readonly viewType = "recode-panel";

  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];

  public static createOrShow(extensionUri: vscode.Uri) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    if (SidebarPanel.currentPanel) {
      SidebarPanel.currentPanel._panel.reveal(column);
      return;
    }

    const panel = vscode.window.createWebviewPanel(
      SidebarPanel.viewType,
      "AI Assistant",
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, "out"),
          vscode.Uri.joinPath(extensionUri, "dist"),
        ],
      }
    );

    SidebarPanel.currentPanel = new SidebarPanel(panel, extensionUri);
  }

  public static kill() {
    SidebarPanel.currentPanel?.dispose();
    SidebarPanel.currentPanel = undefined;
  }

  public static revive(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    SidebarPanel.currentPanel = new SidebarPanel(panel, extensionUri);
  }

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    this._panel = panel;
    this._extensionUri = extensionUri;

    this._update();
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
    this._panel.onDidChangeViewState(
      () => {
        if (this._panel.visible) {
          this._update();
        }
      },
      null,
      this._disposables
    );

    this._panel.webview.onDidReceiveMessage(
      (message) => {
        this._handleMessage(message);
      },
      null,
      this._disposables
    );
  }

  public sendMessage(message: any) {
    this._panel.webview.postMessage(message);
  }

  public dispose() {
    SidebarPanel.currentPanel = undefined;
    this._panel.dispose();
    while (this._disposables.length) {
      const disposable = this._disposables.pop();
      if (disposable) {
        disposable.dispose();
      }
    }
  }

  private _update() {
    const webview = this._panel.webview;
    this._panel.webview.html = this._getHtmlForWebview(webview);
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    const scriptUri = getUri(webview, this._extensionUri, [
      "out",
      "webview.js",
    ]);
    const nonce = getNonce();

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src ${webview.cspSource} https:; font-src ${webview.cspSource};">
            <title>ReCode</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 20px;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .header h1 {
                    color: var(--vscode-textLink-foreground);
                    margin-bottom: 10px;
                }
                .tabs {
                    display: flex;
                    border-bottom: 1px solid var(--vscode-panel-border);
                    margin-bottom: 20px;
                }
                .tab {
                    padding: 10px 20px;
                    cursor: pointer;
                    border: none;
                    background: none;
                    color: var(--vscode-foreground);
                    border-bottom: 2px solid transparent;
                }
                .tab.active {
                    border-bottom-color: var(--vscode-textLink-foreground);
                    color: var(--vscode-textLink-foreground);
                }
                .tab:hover {
                    background-color: var(--vscode-list-hoverBackground);
                }
                .tab-content {
                    display: none;
                }
                .tab-content.active {
                    display: block;
                }
                .settings-form {
                    max-width: 600px;
                    margin: 0 auto;
                }
                .form-group {
                    margin-bottom: 20px;
                }
                .form-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                }
                .form-group select,
                .form-group input {
                    width: 100%;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 3px;
                }
                .btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    margin-right: 10px;
                }
                .btn-primary {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                .btn-primary:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .indexing-section {
                    text-align: center;
                    padding: 40px 20px;
                }
                .indexing-cards {
                    display: flex;
                    gap: 20px;
                    justify-content: center;
                    margin: 30px 0;
                }
                .indexing-card {
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 8px;
                    padding: 20px;
                    min-width: 250px;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .indexing-card:hover {
                    background-color: var(--vscode-list-hoverBackground);
                    border-color: var(--vscode-textLink-foreground);
                }
                .indexing-card.selected {
                    border-color: var(--vscode-textLink-foreground);
                    background-color: var(--vscode-list-activeSelectionBackground);
                }
                .chat-container {
                    height: 500px;
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 8px;
                    display: flex;
                    flex-direction: column;
                }
                .chat-messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 20px;
                }
                .chat-input-container {
                    border-top: 1px solid var(--vscode-panel-border);
                    padding: 15px;
                    display: flex;
                    gap: 10px;
                }
                .chat-input {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 3px;
                }
                .message {
                    margin-bottom: 15px;
                    padding: 10px;
                    border-radius: 8px;
                }
                .message.user {
                    background-color: var(--vscode-textBlockQuote-background);
                    margin-left: 20%;
                }
                .message.assistant {
                    background-color: var(--vscode-editor-selectionBackground);
                    margin-right: 20%;
                }
                .hidden {
                    display: none !important;
                }
                .progress-container {
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 8px;
                    background-color: var(--vscode-editor-selectionBackground);
                }
                .progress-step {
                    margin: 10px 0;
                    padding: 8px;
                    border-radius: 4px;
                }
                .progress-step.active {
                    background-color: var(--vscode-list-activeSelectionBackground);
                    color: var(--vscode-list-activeSelectionForeground);
                }
                .progress-step.completed {
                    background-color: var(--vscode-testing-iconPassed);
                    color: white;
                }
                .current-file-context {
                    margin-bottom: 15px;
                }
                .file-tag {
                    display: inline-flex;
                    align-items: center;
                    background-color: var(--vscode-badge-background);
                    color: var(--vscode-badge-foreground);
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                }
                .file-name {
                    margin-right: 6px;
                }
                .file-remove {
                    background: none;
                    border: none;
                    color: var(--vscode-badge-foreground);
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 0;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                }
                .file-remove:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
                .chat-toggle-container {
                    margin-top: 10px;
                    display: flex;
                    justify-content: flex-end;
                }
                .chat-toggle {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    user-select: none;
                }
                .chat-toggle input[type="checkbox"] {
                    display: none;
                }
                .toggle-slider {
                    position: relative;
                    width: 44px;
                    height: 24px;
                    background-color: var(--vscode-input-background);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    margin-right: 8px;
                }
                .toggle-slider::before {
                    content: '';
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    width: 18px;
                    height: 18px;
                    background-color: var(--vscode-foreground);
                    border-radius: 50%;
                    transition: transform 0.2s ease;
                }
                .chat-toggle input[type="checkbox"]:checked + .toggle-slider {
                    background-color: var(--vscode-button-background);
                    border-color: var(--vscode-button-background);
                }
                .chat-toggle input[type="checkbox"]:checked + .toggle-slider::before {
                    transform: translateX(20px);
                    background-color: white;
                }
                .toggle-label {
                    font-size: 14px;
                    font-weight: 500;
                    color: var(--vscode-foreground);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>ReCode AI Assistant</h1>
                    <p>Modernize your legacy code with AI-powered agents</p>
                </div>

                <div class="tabs">
                    <button class="tab active" data-tab="settings">Settings</button>
                    <button class="tab" data-tab="indexing">Index Codebase</button>
                    <button class="tab" data-tab="ask">Ask</button>
                    <button class="tab" data-tab="code">Code</button>
                </div>

                <!-- Settings Tab -->
                <div class="tab-content active" id="settings-tab">
                    <div class="settings-form">
                        <h2>Configuration</h2>
                        <div class="form-group">
                            <label for="llm-provider">LLM Provider</label>
                            <select id="llm-provider">
                                <option value="azure-openai">Azure OpenAI</option>
                                <option value="google-gemini">Google Gemini</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="llm-model">Model</label>
                            <select id="llm-model">
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="api-key">API Key</label>
                            <input type="password" id="api-key" placeholder="Enter your API key">
                        </div>
                        <button class="btn btn-primary" id="save-settings">Save Settings</button>
                        <button class="btn" id="test-connection">Test Connection</button>
                    </div>
                </div>

                <!-- Indexing Tab -->
                <div class="tab-content" id="indexing-tab">
                    <div class="indexing-section">
                        <h2>Index Your Codebase</h2>
                        <p>Choose how you want to index your codebase for AI analysis</p>

                        <div class="indexing-cards">
                            <div class="indexing-card" data-type="workspace">
                                <h3>Current Workspace</h3>
                                <p>Index the currently opened VSCode workspace</p>
                            </div>
                            <div class="indexing-card" data-type="github">
                                <h3>GitHub Repository</h3>
                                <p>Index a repository from GitHub URL</p>
                            </div>
                        </div>

                        <div id="github-input" class="form-group hidden">
                            <label for="github-url">GitHub Repository URL</label>
                            <input type="text" id="github-url" placeholder="https://github.com/user/repo">
                        </div>

                        <button class="btn btn-primary" id="start-indexing" disabled>Start Indexing</button>

                        <div id="progress-container" class="progress-container hidden">
                            <h3>Indexing Progress</h3>
                            <div class="progress-step" id="step-connecting">Connecting to repository...</div>
                            <div class="progress-step" id="step-analyzing">Analyzing code structure...</div>
                            <div class="progress-step" id="step-embeddings">Creating embeddings...</div>
                            <div class="progress-step" id="step-storing">Storing in vector database...</div>
                        </div>
                    </div>
                </div>

                <!-- Ask Tab -->
                <div class="tab-content" id="ask-tab">
                    <h2>Ask Questions About Your Code</h2>
                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            <div class="message assistant">
                                <strong>ReCode Assistant:</strong> Hello! I'm ready to help you understand your codebase. Ask me anything about your code structure, functionality, or potential improvements.
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <input type="text" class="chat-input" id="chat-input" placeholder="Ask about your codebase...">
                            <button class="btn btn-primary" id="send-message">Send</button>
                        </div>
                    </div>
                </div>

                <!-- Code Tab -->
                <div class="tab-content" id="code-tab">
                    <h2>Generate & Improve Code</h2>

                    <!-- Current File Context -->
                    <div id="current-file-context" class="current-file-context hidden">
                        <div class="file-tag">
                            <span class="file-name" id="current-file-name"></span>
                            <button class="file-remove" id="remove-current-file">×</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="code-request">Describe what you want to generate or improve:</label>
                        <textarea id="code-request" rows="18" style="width: 100%; padding: 12px; border: 1px solid var(--vscode-input-border); background-color: var(--vscode-input-background); color: var(--vscode-input-foreground); border-radius: 3px; font-family: var(--vscode-font-family); font-size: var(--vscode-font-size); line-height: 1.5; resize: vertical; min-height: 400px;" placeholder="Example: Add error handling to the payment processing module"></textarea>

                        <!-- Chat Toggle -->
                        <div class="chat-toggle-container">
                            <label class="chat-toggle">
                                <input type="checkbox" id="chat-mode-toggle">
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Chat</span>
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-primary" id="generate-code">Generate Code</button>

                    <div id="code-progress" class="progress-container hidden">
                        <h3>Code Generation Progress</h3>
                        <div class="progress-step" id="code-step-thinking">Thinking...</div>
                        <div class="progress-step" id="code-step-analyzing">Analyzing Directory Structure...</div>
                        <div class="progress-step" id="code-step-searching">Searching Relevant Code...</div>
                        <div class="progress-step" id="code-step-generating">Generating Code...</div>
                        <div class="progress-step" id="code-step-reviewing">Code Review in Progress...</div>
                    </div>
                </div>
            </div>

            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
  }

  private async _handleMessage(message: any) {
    switch (message.command) {
      case "alert":
        vscode.window.showInformationMessage(message.text);
        break;
      case "error":
        vscode.window.showErrorMessage(message.text);
        break;
      case "openFile":
        await this._openFile(message.filePath, message.line);
        break;
      case "getWorkspaceInfo":
        this._getWorkspaceInfo();
        break;
      case "saveSettings":
        await this._saveSettings(message.settings);
        break;
      case "getSettings":
        this._getSettings();
        break;
      case "testConnection":
        await this._testConnection();
        break;
      case "startIndexing":
        await this._startIndexing(message.data);
        break;
      case "askQuestion":
        await this._askQuestion(message.question);
        break;
      case "generateCode":
        await this._generateCode(message.request);
        break;
      default:
        console.log("Unknown message command:", message.command);
    }
  }

  private async _openFile(filePath: string, line?: number) {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        vscode.window.showErrorMessage("No workspace folder is open");
        return;
      }

      const fullPath = vscode.Uri.joinPath(workspaceFolders[0].uri, filePath);
      const document = await vscode.workspace.openTextDocument(fullPath);
      const editor = await vscode.window.showTextDocument(document);

      if (line && line > 0) {
        const position = new vscode.Position(line - 1, 0);
        editor.selection = new vscode.Selection(position, position);
        editor.revealRange(new vscode.Range(position, position));
      }
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to open file: ${filePath}`);
      console.error("Error opening file:", error);
    }
  }

  private _getWorkspaceInfo() {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    const workspaceInfo = {
      hasWorkspace: !!workspaceFolders,
      workspaceName: workspaceFolders?.[0]?.name || "",
      workspacePath: workspaceFolders?.[0]?.uri.fsPath || "",
    };

    this.sendMessage({
      command: "workspaceInfo",
      data: workspaceInfo,
    });
  }

  private async _saveSettings(settings: any) {
    const config = vscode.workspace.getConfiguration("recode");

    try {
      await config.update(
        "llmProvider",
        settings.provider,
        vscode.ConfigurationTarget.Global
      );
      await config.update(
        "llmModel",
        settings.model,
        vscode.ConfigurationTarget.Global
      );
      await config.update(
        "apiKey",
        settings.apiKey,
        vscode.ConfigurationTarget.Global
      );

      this.sendMessage({
        command: "settingsSaved",
        success: true,
      });
    } catch (error) {
      console.error("Error saving settings:", error);
      this.sendMessage({
        command: "settingsSaved",
        success: false,
        error: "Failed to save settings",
      });
    }
  }

  private _getSettings() {
    const config = vscode.workspace.getConfiguration("recode");

    const settings = {
      provider: config.get("llmProvider", "azure-openai"),
      model: config.get("llmModel", "gpt-4"),
      apiKey: config.get("apiKey", ""),
    };

    this.sendMessage({
      command: "settingsLoaded",
      data: settings,
    });
  }

  private async _testConnection() {
    const config = vscode.workspace.getConfiguration("recode");
    const provider = config.get("llmProvider", "azure-openai");
    const apiKey = config.get("apiKey", "");

    if (!apiKey) {
      vscode.window.showErrorMessage("Please configure your API key first");
      return;
    }

    vscode.window.showInformationMessage(
      `Testing connection to ${provider}...`
    );
    // TODO: Implement actual connection test
    setTimeout(() => {
      vscode.window.showInformationMessage("Connection test successful!");
    }, 1000);
  }

  private async _startIndexing(data: any) {
    if (data.type === "workspace") {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        vscode.window.showErrorMessage("No workspace folder is open");
        return;
      }
      vscode.window.showInformationMessage(
        `Starting indexing of workspace: ${workspaceFolders[0].name}`
      );
    } else if (data.type === "github") {
      vscode.window.showInformationMessage(
        `Starting indexing of GitHub repository: ${data.url}`
      );
    }

    // TODO: Implement actual indexing logic
    // For now, just show a success message after a delay
    setTimeout(() => {
      vscode.window.showInformationMessage(
        "Codebase indexing completed successfully!"
      );
    }, 8000);
  }

  private async _askQuestion(question: string) {
    vscode.window.showInformationMessage(`Processing question: ${question}`);

    // TODO: Implement actual AI question processing
    // For now, send a mock response
    setTimeout(() => {
      this.sendMessage({
        command: "chatResponse",
        response: `I understand you're asking about: "${question}". This is a placeholder response. The actual AI integration will be implemented to analyze your codebase and provide detailed answers.`,
      });
    }, 2000);
  }

  private async _generateCode(request: string) {
    vscode.window.showInformationMessage(
      `Processing code generation request: ${request}`
    );

    // TODO: Implement actual AI code generation
    // For now, just show a completion message
    setTimeout(() => {
      vscode.window.showInformationMessage(
        "Code generation completed! Results would be displayed in the interface."
      );
    }, 6000);
  }
}
