import * as vscode from "vscode";
import { SidebarProvider } from "./providers/SidebarProvider";
import { indexingService } from "./indexing";
import { SessionAwareWebSocketClient } from "./websocketClient";
import { SessionCommands } from "./commands";
import { SessionManager } from "./sessionManager";

export function activate(context: vscode.ExtensionContext) {
  console.log("🚀 AI Coding Assistant extension is now active!");
  vscode.window.showInformationMessage(
    "AI Coding Assistant extension activated successfully!"
  );

  // Initialize session management
  console.log("🔑 Initializing session management...");
  const sessionManager = SessionManager.getInstance(context);
  
  // Initialize WebSocket client with session support
  const serverUrl = vscode.workspace.getConfiguration('recode.ai').get<string>('serverUrl', 'ws://localhost:8000');
  const wsClient = new SessionAwareWebSocketClient(context, serverUrl);
  
  // Initialize session commands
  const sessionCommands = new SessionCommands(context, wsClient);
  
  console.log("✅ Session management initialized!");

  // Register the sidebar webview provider
  console.log("📝 Registering sidebar provider...");
  const sidebarProvider = new SidebarProvider(context.extensionUri, context);

  // Listen for workspace folder changes and notify the sidebar
  // Only trigger on actual workspace folder additions/removals, not file changes
  vscode.workspace.onDidChangeWorkspaceFolders((event) => {
    console.log("🔄 Workspace folders changed:", {
      added: event.added.length,
      removed: event.removed.length,
    });

    // Only send workspaceChanged if actual workspace folders were added/removed
    if (event.added.length > 0 || event.removed.length > 0) {
      console.log(
        "🔄 Sending workspaceChanged message due to folder add/remove"
      );
      sidebarProvider.sendMessage({
        command: "workspaceChanged",
      });
    } else {
      console.log("🔄 Ignoring workspace change - no folders added/removed");
    }
  });

  try {
    const disposable = vscode.window.registerWebviewViewProvider(
      SidebarProvider.viewType,
      sidebarProvider
    );

    context.subscriptions.push(disposable);
    console.log("✅ Sidebar provider registered successfully!");
  } catch (error) {
    console.error("❌ Error registering sidebar provider:", error);
    vscode.window.showErrorMessage(
      `AI Coding Assistant extension failed to activate: ${error}`
    );
    return; // Exit early if registration fails
  }

  // Track active editor changes and send to sidebar
  const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor(
    (editor) => {
      if (editor) {
        const filePath = vscode.workspace.asRelativePath(editor.document.uri);
        const fileName = editor.document.uri.path.split("/").pop() || "";

        sidebarProvider.sendMessage({
          command: "activeFileChanged",
          data: {
            fileName: fileName,
            filePath: filePath,
            fullPath: editor.document.uri.fsPath,
          },
        });
      }
    }
  );

  // Register commands for backward compatibility and direct actions
  const indexWorkspaceCommand = vscode.commands.registerCommand(
    "recode.indexWorkspace",
    async () => {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        vscode.window.showErrorMessage("No workspace folder is open");
        return;
      }

      vscode.window.showInformationMessage("Starting workspace indexing...");
      sidebarProvider.sendMessage({
        command: "startIndexing",
        data: { type: "workspace" },
      });
    }
  );

  // Full indexing command
  const fullIndexCommand = vscode.commands.registerCommand(
    "recode.fullIndex",
    async () => {
      try {
        vscode.window.showInformationMessage("Starting full indexing...");
        await indexingService.performFullIndex();
      } catch (error) {
        console.error("Full index failed:", error);
        vscode.window.showErrorMessage(`Full indexing failed: ${error}`);
      }
    }
  );

  // Differential sync command
  const diffSyncCommand = vscode.commands.registerCommand(
    "recode.differentialSync",
    async () => {
      try {
        vscode.window.showInformationMessage("Starting differential sync...");
        await indexingService.performDifferentialSync();
      } catch (error) {
        console.error("Differential sync failed:", error);
        vscode.window.showErrorMessage(`Differential sync failed: ${error}`);
      }
    }
  );

  context.subscriptions.push(
    indexWorkspaceCommand,
    fullIndexCommand,
    diffSyncCommand,
    onDidChangeActiveTextEditor
  );
}

export function deactivate() {
  // Cleanup if needed
  console.log("🛑 AI Coding Assistant extension is being deactivated");
}
