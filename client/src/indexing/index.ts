// Export all indexing functionality
export * from "./models";
export * from "./utils";
export * from "./chunker";
export * from "./merkleTree";
export * from "./indexClient";
export * from "./sync";

// Main indexing service class
import { Lang<PERSON>hainChunker } from "./chunker";
import { MerkleTreeBuilder } from "./merkleTree";
import { SyncClient } from "./sync";
import {
  getWorkspaceInfo,
  scanFiles,
  createFolderEntries,
  createFileEntries,
  handleIndexingError,
  showIndexingProgress,
  showIndexingSuccess,
} from "./utils";
import { FileEntry, FileMetadata } from "./models";

export class IndexingService {
  private syncClient: SyncClient | null = null;
  private merkleTreeBuilder: MerkleTreeBuilder | null = null;
  private chunker: LangChainChunker | null = null;

  /**
   * Initialize the indexing service for a workspace
   */
  public async initialize(workspaceRoot: string): Promise<void> {
    try {
      this.syncClient = new SyncClient(workspaceRoot);
      this.merkleTreeBuilder = new MerkleTreeBuilder(workspaceRoot);
      this.chunker = new LangChainChunker();

      showIndexingProgress("AI Assistant indexing service initialized");
    } catch (error) {
      handleIndexingError(error, "initialize");
      throw error;
    }
  }

  /**
   * Perform full indexing of the workspace
   */
  public async performFullIndex(): Promise<void> {
    try {
      const workspaceInfo = getWorkspaceInfo();
      if (!workspaceInfo) {
        throw new Error("No workspace folder is open");
      }

      const { workspaceRoot, projectId } = workspaceInfo;

      if (!this.syncClient || !this.merkleTreeBuilder || !this.chunker) {
        await this.initialize(workspaceRoot);
      }

      showIndexingProgress("Scanning workspace files...");

      // Scan workspace files
      const fileMetadata = await scanFiles(workspaceRoot, this.chunker!);

      if (!fileMetadata.length) {
        showIndexingSuccess("No files found to index");
        return;
      }

      showIndexingProgress(`Found ${fileMetadata.length} files to index`);

      // Transform FileMetadata to FileEntry format
      const filesToIndex: FileEntry[] = fileMetadata.map((file) => ({
        filePath: file.actualPath,
        fileHash: file.fileHash,
        chunkHashes: file.chunks.map((chunk) => chunk.chunkHash),
        lastModified: Date.now(),
        size: file.chunks.reduce(
          (total, chunk) => total + chunk.text.length,
          0
        ),
        type: "file" as const,
        chunks: file.chunks.map((chunk) => ({
          text: chunk.text,
          chunkHash: chunk.chunkHash,
          lineRange: chunk.lineRange,
        })),
        obfuscatedPath: file.obfuscatedPath,
        language: file.language,
      }));

      // Create folder entries
      const folderEntries = createFolderEntries(fileMetadata, workspaceRoot);

      // Combine all entries
      const allEntries = [...filesToIndex, ...folderEntries];

      // Build Merkle tree
      const merkleTree = this.merkleTreeBuilder!.buildFromFiles(allEntries);

      showIndexingProgress(
        `Built Merkle tree with root: ${merkleTree.root.substring(0, 8)}...`
      );

      // Call the index method
      await this.syncClient!.index(projectId, allEntries, merkleTree);

      showIndexingSuccess(`Successfully indexed ${fileMetadata.length} files`);
    } catch (error) {
      handleIndexingError(error, "performFullIndex");
      throw error;
    }
  }

  /**
   * Perform differential sync
   */
  public async performDifferentialSync(): Promise<void> {
    try {
      const workspaceInfo = getWorkspaceInfo();
      if (!workspaceInfo) {
        throw new Error("No workspace folder is open");
      }

      const { workspaceRoot, projectId } = workspaceInfo;

      if (!this.syncClient || !this.merkleTreeBuilder || !this.chunker) {
        await this.initialize(workspaceRoot);
      }

      showIndexingProgress("Scanning workspace for changes...");

      // Scan workspace files
      const fileMetadata = await scanFiles(workspaceRoot, this.chunker!);

      // Create file entries
      const fileEntries = await createFileEntries(
        fileMetadata,
        workspaceRoot,
        this.merkleTreeBuilder!
      );

      // Create folder entries
      const folderEntries = createFolderEntries(fileMetadata, workspaceRoot);

      // Combine all entries
      const allEntries = [...fileEntries, ...folderEntries];

      showIndexingProgress(
        `Analyzing ${allEntries.length} entries for changes...`
      );

      // Use differential sync
      await this.syncClient!.differentialSync(projectId, allEntries);

      showIndexingSuccess("Successfully completed differential sync");
    } catch (error) {
      handleIndexingError(error, "performDifferentialSync");
      throw error;
    }
  }

  /**
   * Check if the service is initialized
   */
  public isInitialized(): boolean {
    return !!(this.syncClient && this.merkleTreeBuilder && this.chunker);
  }

  /**
   * Get service status
   */
  public getStatus(): {
    initialized: boolean;
    workspaceRoot?: string;
  } {
    const workspaceInfo = getWorkspaceInfo();
    return {
      initialized: this.isInitialized(),
      workspaceRoot: workspaceInfo?.workspaceRoot,
    };
  }
}

// Export singleton instance
export const indexingService = new IndexingService();
