// Mock service for dummy API responses during development
import {
  IndexBatchResponse,
  LatestMerkleTreeResponse,
  DifferentialSyncResponse,
  MerkleTree,
} from "./models";

export class MockApiService {
  private static instance: MockApiService;
  private mockDelay: number = 500; // Simulate network delay

  private constructor() {}

  public static getInstance(): MockApiService {
    if (!MockApiService.instance) {
      MockApiService.instance = new MockApiService();
    }
    return MockApiService.instance;
  }

  /**
   * Set mock delay for simulating network latency
   */
  public setMockDelay(delay: number): void {
    this.mockDelay = delay;
  }

  /**
   * Simulate network delay
   */
  private async delay(): Promise<void> {
    if (this.mockDelay > 0) {
      await new Promise((resolve) => setTimeout(resolve, this.mockDelay));
    }
  }

  // REMOVED: mockIndexInit - no longer needed with stateless design

  /**
   * Mock index batch response
   */
  public async mockIndexBatch(
    projectId: string,
    batchIndex: number,
    fileCount: number
  ): Promise<IndexBatchResponse> {
    await this.delay();

    console.log(
      `🎭 [MOCK] Index batch ${
        batchIndex + 1
      } called for project: ${projectId}, files: ${fileCount}`
    );

    return {
      status: "success",
      message: `Batch ${batchIndex + 1} processed successfully (MOCK RESPONSE)`,
      batch_index: batchIndex,
      processed_files: fileCount,
    };
  }

  /**
   * Mock latest Merkle tree response
   */
  public async mockLatestMerkleTree(
    projectId: string
  ): Promise<LatestMerkleTreeResponse> {
    await this.delay();

    console.log(
      `🎭 [MOCK] Latest Merkle tree requested for project: ${projectId}`
    );

    // Simulate different scenarios
    const scenarios = [
      // No previous tree (first time indexing)
      {
        status: "success" as const,
        merkle_tree: null,
        message:
          "No previous Merkle tree found (MOCK RESPONSE - First time indexing)",
      },
      // Previous tree exists (for differential sync testing)
      {
        status: "success" as const,
        merkle_tree: {
          root: "mock_root_hash_12345",
          leaves: ["leaf1", "leaf2", "leaf3"],
          tree: {
            level_0_pos_0: "mock_root_hash_12345",
            leaf_0: "leaf1",
            leaf_1: "leaf2",
            leaf_2: "leaf3",
          },
          fileMap: {
            "/mock/file1.ts": {
              filePath: "/mock/file1.ts",
              fileHash: "mock_file_hash_1",
              chunkHashes: ["chunk1", "chunk2"],
              lastModified: Date.now() - 86400000, // 1 day ago
              size: 1000,
              type: "file" as const,
              chunks: [],
              obfuscatedPath: "mock_obfuscated_1",
              language: "typescript",
            },
          },
          timestamp: Date.now() - 86400000, // 1 day ago
        } as MerkleTree,
        message: "Previous Merkle tree found (MOCK RESPONSE)",
      },
    ];

    // Return first scenario (no previous tree) for now
    // You can modify this logic to test different scenarios
    return scenarios[0];
  }

  /**
   * Mock differential sync response
   */
  public async mockDifferentialSync(
    projectId: string,
    addedCount: number,
    modifiedCount: number,
    deletedCount: number
  ): Promise<DifferentialSyncResponse> {
    await this.delay();

    console.log(`🎭 [MOCK] Differential sync called for project: ${projectId}`);
    console.log(
      `🎭 [MOCK] Changes - Added: ${addedCount}, Modified: ${modifiedCount}, Deleted: ${deletedCount}`
    );

    return {
      status: "success",
      message: "Differential sync completed successfully (MOCK RESPONSE)",
      processed_changes: {
        added: addedCount,
        modified: modifiedCount,
        deleted: deletedCount,
      },
    };
  }

  /**
   * Mock error response for testing error handling
   */
  public async mockErrorResponse(errorMessage: string): Promise<never> {
    await this.delay();

    console.log(`🎭 [MOCK] Simulating error: ${errorMessage}`);
    throw new Error(`Mock API Error: ${errorMessage}`);
  }

  /**
   * Get mock configuration
   */
  public getMockConfig(): {
    delay: number;
    enabled: boolean;
  } {
    return {
      delay: this.mockDelay,
      enabled: true, // Always enabled in development
    };
  }

  /**
   * Log mock service status
   */
  public logStatus(): void {
    console.log(`🎭 [MOCK SERVICE] Status:`);
    console.log(`    Enabled: true`);
    console.log(`    Delay: ${this.mockDelay}ms`);
    console.log(`    Available endpoints:`);
    console.log(`      - /index/init`);
    console.log(`      - /index/batch`);
    console.log(`      - /index/latest_merkle_tree`);
    console.log(`      - /sync/differential`);
  }

  /**
   * Simulate different response scenarios for testing
   */
  public setScenario(
    scenario: "success" | "error" | "slow" | "first-time" | "has-previous"
  ): void {
    switch (scenario) {
      case "success":
        this.mockDelay = 100;
        break;
      case "error":
        this.mockDelay = 200;
        // You can add error simulation logic here
        break;
      case "slow":
        this.mockDelay = 2000;
        break;
      case "first-time":
        this.mockDelay = 500;
        // Logic for first-time indexing scenario
        break;
      case "has-previous":
        this.mockDelay = 500;
        // Logic for when previous Merkle tree exists
        break;
    }

    console.log(
      `🎭 [MOCK] Scenario set to: ${scenario} (delay: ${this.mockDelay}ms)`
    );
  }
}

// Export singleton instance
export const mockApiService = MockApiService.getInstance();

// Initialize mock service
mockApiService.logStatus();
