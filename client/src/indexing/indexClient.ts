import { FileEntry, MerkleTree, IndexingConfig } from "./models";
import {
  compressPayload,
  createBatches,
  logBatchBreakdown,
  loadIndexingConfig,
  handleIndexingError,
  showIndexingProgress,
  showIndexingSuccess,
} from "./utils";

export class IndexManager {
  private config: IndexingConfig;

  constructor() {
    this.config = loadIndexingConfig();
  }

  /**
   * Process full indexing with batching and compression
   */
  async processFullIndex(
    projectId: string,
    files: FileEntry[],
    merkleTree: MerkleTree
  ): Promise<void> {
    try {
      showIndexingProgress(`Starting full index for project ${projectId}`);

      // Check if there are no files to process
      if (files.length === 0) {
        console.log("ℹ️ No files to index - skipping server calls");
        showIndexingProgress("No files to sync with server");
        return;
      }

      const batches = createBatches(files, this.config.batchSize);
      const batchCount = batches.length;

      console.log(
        `📦 Created ${batchCount} batches from ${files.length} files`
      );
      logBatchBreakdown(batches);

      // Send each batch directly - no initialization needed (stateless design)
      for (let i = 0; i < batches.length; i++) {
        const batchChunkCount = batches[i].reduce(
          (sum: number, file: any) =>
            sum + (file.chunks ? file.chunks.length : 0),
          0
        );

        showIndexingProgress(
          `Sending batch ${i + 1}/${batches.length} with ${
            batches[i].length
          } files and ${batchChunkCount} chunks`
        );

        await this.sendIndexBatch(projectId, batches[i], i, merkleTree);
      }

      showIndexingSuccess(
        `Successfully indexed ${files.length} files in ${batchCount} batches`
      );
    } catch (error) {
      handleIndexingError(error, "processFullIndex");
      throw error;
    }
  }

  // REMOVED: initializeIndex method - using stateless design with in-memory queue

  /**
   * Send a batch of files for indexing
   */
  private async sendIndexBatch(
    projectId: string,
    files: FileEntry[],
    batchIndex: number,
    merkleTree: MerkleTree
  ): Promise<void> {
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        const totalChunksInBatch = files.reduce(
          (sum: number, file: any) =>
            sum + (file.chunks ? file.chunks.length : 0),
          0
        );

        console.log(
          `📤 [${new Date().toISOString()}] Sending index batch ${
            batchIndex + 1
          } with ${
            files.length
          } files and ${totalChunksInBatch} chunks (attempt ${attempt}/${
            this.config.maxRetries
          })`
        );

        // DEBUG: Log what we're sending to server
        console.log(`🔍 DEBUG: Sending to server - batch ${batchIndex + 1}:`);
        console.log(`  Files count: ${files.length}`);
        if (files.length > 0) {
          console.log(`  First file filePath:`, files[0].filePath);
          console.log(`  First file obfuscatedPath:`, files[0].obfuscatedPath);
          console.log(`  First file chunks count:`, files[0].chunks.length);
          console.log(`  First file type:`, files[0].type);
        }

        const payload = this.createIndexPayload(
          projectId,
          files, // Send FileEntry directly - no transformation needed!
          batchIndex,
          merkleTree
        );

        const compressed = compressPayload(payload);
        console.log(
          `🗜️ [${new Date().toISOString()}] Compressed index payload size: ${
            compressed.length
          } chars`
        );

        const response = await fetch(
          `${this.config.baseUrl}${this.config.indexBatchEndpoint}`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ compressed }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Failed to send index batch ${batchIndex + 1}: ${
              response.status
            } - ${errorText}`
          );
        }

        const result = await response.json();
        console.log(
          `✅ [${new Date().toISOString()}] Index batch ${
            batchIndex + 1
          } sent successfully:`,
          result
        );
        return;
      } catch (error) {
        console.error(
          `❌ [${new Date().toISOString()}] Attempt ${attempt} failed for index batch ${
            batchIndex + 1
          }: ${error}`
        );

        if (attempt === this.config.maxRetries) {
          throw error;
        }

        // Wait before retrying
        await new Promise((resolve) =>
          setTimeout(resolve, this.config.retryDelay)
        );
      }
    }
  }

  // No transformation needed - send FileEntry directly to server!

  /**
   * Create the index payload structure
   */
  private createIndexPayload(
    projectId: string,
    transformedFiles: any[],
    batchIndex: number,
    merkleTree: MerkleTree
  ): any {
    return {
      project_id: projectId,
      files: transformedFiles,
      batch_index: batchIndex,
      merkle_tree: merkleTree,
    };
  }

  /**
   * Get indexing statistics
   */
  getIndexingStats(files: FileEntry[]): {
    totalFiles: number;
    totalChunks: number;
    totalSize: number;
    fileTypes: { [key: string]: number };
  } {
    const totalFiles = files.length;
    const totalChunks = files.reduce(
      (sum, file) => sum + file.chunks.length,
      0
    );
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    const fileTypes: { [key: string]: number } = {};
    files.forEach((file) => {
      if (file.language) {
        fileTypes[file.language] = (fileTypes[file.language] || 0) + 1;
      }
    });

    return {
      totalFiles,
      totalChunks,
      totalSize,
      fileTypes,
    };
  }

  /**
   * Validate files before indexing
   */
  validateFiles(files: FileEntry[]): { valid: FileEntry[]; invalid: string[] } {
    const valid: FileEntry[] = [];
    const invalid: string[] = [];

    files.forEach((file) => {
      if (!file.filePath || !file.fileHash) {
        invalid.push(`Missing required fields: ${file.filePath}`);
      } else if (
        file.type === "file" &&
        (!file.chunks || file.chunks.length === 0)
      ) {
        invalid.push(`No chunks found: ${file.filePath}`);
      } else {
        valid.push(file);
      }
    });

    return { valid, invalid };
  }
}
