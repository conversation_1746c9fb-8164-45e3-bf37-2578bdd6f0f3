import {
  <PERSON><PERSON><PERSON>ry,
  ChangeDiff,
  MerkleTree,
  LatestMerkleTreeResponse,
  DifferentialSyncResponse,
  IndexingConfig,
} from "./models";
import { LangChainChunker } from "./chunker";
import { MerkleTreeBuilder } from "./merkleTree";
import { IndexManager } from "./indexClient";
import {
  compressPayload,
  createBatches,
  logFileAndChunkDetails,
  loadIndexingConfig,
  handleIndexingError,
  showIndexingProgress,
  showIndexingSuccess,
} from "./utils";

export class SyncClient {
  private config: IndexingConfig;
  private merkleTreeBuilder: MerkleTreeBuilder;
  private chunker: LangChainChunker;
  private workspaceRoot: string;
  private indexManager: IndexManager;

  constructor(workspaceRoot: string) {
    this.config = loadIndexingConfig();
    this.merkleTreeBuilder = new MerkleTreeBuilder(workspaceRoot);
    this.chunker = new LangChainChunker();
    this.workspaceRoot = workspaceRoot;
    this.indexManager = new IndexManager();

    console.log(
      `🚀 [${new Date().toISOString()}] SyncClient initialized with baseUrl: ${
        this.config.baseUrl
      } and workspaceRoot: ${workspaceRoot}`
    );
  }

  /**
   * Get the latest Merkle tree from the server
   */
  public async getLatestMerkleTree(
    projectId: string
  ): Promise<MerkleTree | null> {
    try {
      console.log(
        `📡 [${new Date().toISOString()}] Fetching latest Merkle tree for project: ${projectId}`
      );

      const response = await fetch(
        `${this.config.baseUrl}/index/latest_merkle_tree?project_id=${projectId}`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.log(
            `[${new Date().toISOString()}] No previous Merkle tree found for project ${projectId}`
          );
          return null;
        }
        const errorText = await response.text();
        console.error(
          `[${new Date().toISOString()}] Failed to fetch latest Merkle tree: ${
            response.status
          } - ${errorText}`
        );
        return null;
      }

      const result = await response.json();
      if (result.status === "success" && result.merkle_tree) {
        console.log(
          `[${new Date().toISOString()}] Retrieved latest Merkle tree`
        );
        return result.merkle_tree as MerkleTree;
      } else {
        console.log(
          `[${new Date().toISOString()}] No Merkle tree found for project ${projectId}`
        );
        return null;
      }

      // TODO: Replace with actual API call when backend is ready
      /*
      const response = await fetch(
        `${this.config.baseUrl}/index/latest_merkle_tree?project_id=${projectId}`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.log(
            `[${new Date().toISOString()}] No previous Merkle tree found for project ${projectId}`
          );
          return null;
        }
        const errorText = await response.text();
        console.error(
          `[${new Date().toISOString()}] Failed to fetch latest Merkle tree: ${response.status} - ${errorText}`
        );
        return null;
      }

      const result = await response.json();
      if (result.status === "success" && result.merkle_tree) {
        console.log(
          `[${new Date().toISOString()}] Retrieved latest Merkle tree`
        );
        return result.merkle_tree as MerkleTree;
      } else {
        console.log(
          `[${new Date().toISOString()}] No Merkle tree found for project ${projectId}`
        );
        return null;
      }
      */
    } catch (error) {
      console.error(
        `❌ [${new Date().toISOString()}] Error fetching latest Merkle tree: ${error}`
      );
      return null;
    }
  }

  /**
   * Perform full indexing
   */
  async index(
    projectId: string,
    files: FileEntry[],
    merkleTree: MerkleTree
  ): Promise<void> {
    console.log(
      `=== [${new Date().toISOString()}] SYNC CLIENT INDEX CALLED ===`
    );
    console.log(`Project ID: ${projectId}`);
    console.log(`Files count: ${files.length}`);
    console.log(`Merkle tree root: ${merkleTree.root}`);

    logFileAndChunkDetails(files);

    try {
      await this.indexManager.processFullIndex(projectId, files, merkleTree);
      console.log(
        `✅ [${new Date().toISOString()}] All batches sent successfully!`
      );
    } catch (error) {
      console.error(
        `❌ [${new Date().toISOString()}] Error in sync client index:`,
        error
      );
      throw error;
    }
  }

  /**
   * Perform differential sync
   */
  async differentialSync(
    projectId: string,
    fileEntries: FileEntry[]
  ): Promise<void> {
    console.log(
      `=== [${new Date().toISOString()}] DIFFERENTIAL SYNC STARTED ===`
    );
    console.log(`Project ID: ${projectId}`);
    console.log(`File entries: ${fileEntries.length}`);

    try {
      // Get latest Merkle root from backend
      const previousMerkleTree = await this.getLatestMerkleTree(projectId);

      // Compute diff using MerkleTreeBuilder
      const diff = await this.merkleTreeBuilder.detectChanges(
        fileEntries,
        previousMerkleTree
      );

      console.log(`🔍 [${new Date().toISOString()}] Diff computed:`);
      console.log(
        `    Added: ${diff.added.length}, Modified: ${diff.modified.length}, Deleted: ${diff.deleted.length}`
      );

      const filesToSync = await this.prepareChangedFilesForSync(diff);

      console.log(
        `📤 [${new Date().toISOString()}] Preparing to sync ${
          filesToSync.length
        } CHANGED files only`
      );

      this.logSyncDetails(filesToSync, diff);

      // Send differential sync request
      await this.sendDifferentialSync(projectId, diff, filesToSync);
      console.log(
        `✅ [${new Date().toISOString()}] Differential sync completed for project ${projectId}`
      );
    } catch (error) {
      console.error(
        `❌ [${new Date().toISOString()}] Error in differential sync: ${error}`
      );
      throw error;
    }
  }

  /**
   * Prepare changed files for sync using already-chunked data (no re-chunking needed)
   */
  private async prepareChangedFilesForSync(diff: ChangeDiff): Promise<any[]> {
    // Only sync files that have actually changed (added or modified)
    const changedEntries = [...diff.added, ...diff.modified];

    // Filter to only include files (not folders)
    const changedFiles = changedEntries.filter(
      (entry) => entry.type === "file"
    );

    console.log(
      `🔄 [${new Date().toISOString()}] Preparing ${
        changedFiles.length
      } changed files (using already-chunked data)...`
    );

    // Use already-chunked files - no need to re-chunk!
    const filesToSync = changedFiles.map((entry) => {
      // Use the same payload structure as indexing for consistency
      return {
        filePath: entry.filePath, // Use filePath instead of actualPath
        fileHash: entry.fileHash,
        language: entry.language,
        chunks: entry.chunks,
        type: entry.type,
        obfuscatedPath: entry.obfuscatedPath,
        chunkHashes: entry.chunkHashes,
        lastModified: entry.lastModified,
        size: entry.size,
      };
    });

    console.log(
      `✅ [${new Date().toISOString()}] Prepared ${
        filesToSync.length
      } files for sync (no re-chunking needed)`
    );

    return filesToSync;
  }

  /**
   * Log sync details for debugging
   */
  private logSyncDetails(filesToSync: any[], diff: ChangeDiff): void {
    // Log what we're syncing
    filesToSync.forEach((file, index) => {
      console.log(
        `  ${index + 1}. ${file.obfuscatedPath} (${
          file.chunks.length
        } chunks) - ${file.type}`
      );
    });

    // Also log what we're NOT syncing
    console.log(
      `📋 [${new Date().toISOString()}] Unchanged files (NOT syncing): ${
        diff.unchanged.length
      }`
    );
    diff.unchanged.slice(0, 5).forEach((entry, index) => {
      console.log(`  ${index + 1}. ${entry.filePath} - ${entry.type}`);
    });
  }

  /**
   * Send differential sync request to server
   */
  private async sendDifferentialSync(
    projectId: string,
    diff: ChangeDiff,
    files: any[]
  ): Promise<void> {
    // Check if there are no files to sync
    if (files.length === 0) {
      console.log("ℹ️ No files to sync - skipping server calls");
      return;
    }

    const batches = createBatches(files, this.config.batchSize);
    console.log(
      `📦 [${new Date().toISOString()}] Created ${
        batches.length
      } batches from ${files.length} files`
    );

    for (let i = 0; i < batches.length; i++) {
      const batchChunkCount = batches[i].reduce(
        (sum: number, file: any) =>
          sum + (file.chunks ? file.chunks.length : 0),
        0
      );
      console.log(
        `[${new Date().toISOString()}] Sending batch ${i + 1}/${
          batches.length
        } with ${batches[i].length} files and ${batchChunkCount} chunks`
      );
      await this.sendBatch(projectId, diff, batches[i], i);
    }
  }

  /**
   * Send a single batch for differential sync
   */
  private async sendBatch(
    projectId: string,
    diff: ChangeDiff,
    files: any[],
    batchIndex: number
  ): Promise<void> {
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        const totalChunksInBatch = files.reduce(
          (sum: number, file: any) =>
            sum + (file.chunks ? file.chunks.length : 0),
          0
        );
        console.log(
          `[${new Date().toISOString()}] Sending batch ${batchIndex + 1} with ${
            files.length
          } files and ${totalChunksInBatch} chunks (attempt ${attempt}/${
            this.config.maxRetries
          })`
        );

        // Use files directly - same payload structure as indexing
        const payload = this.createDifferentialSyncPayload(
          projectId,
          diff,
          files, // No transformation needed - use FileEntry directly
          batchIndex
        );

        this.logBatchPayload(payload, batchIndex);

        const compressed = compressPayload(payload);
        console.log(
          `📦 [${new Date().toISOString()}] Compressed payload size: ${
            compressed.length
          } chars`
        );

        const response = await fetch(
          `${this.config.baseUrl}/sync/differential`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ compressed }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Failed to send batch ${batchIndex + 1}: ${
              response.status
            } - ${errorText}`
          );
        }

        const result = await response.json();
        console.log(
          `✅ [${new Date().toISOString()}] Batch ${
            batchIndex + 1
          } sent successfully:`,
          result
        );
        return;
      } catch (error) {
        console.error(
          `[${new Date().toISOString()}] Attempt ${attempt} failed for batch ${
            batchIndex + 1
          }: ${error}`
        );
        if (attempt === this.config.maxRetries) {
          throw error;
        }
        await new Promise((resolve) =>
          setTimeout(resolve, this.config.retryDelay)
        );
      }
    }
  }

  // No transformation needed - use FileEntry directly for consistency with indexing

  /**
   * Create differential sync payload
   */
  private createDifferentialSyncPayload(
    projectId: string,
    diff: ChangeDiff,
    files: any[], // Changed from transformedFiles to files
    batchIndex: number
  ): any {
    return {
      project_id: projectId,
      merkle_tree: diff.currentTree,
      changes: {
        added: diff.added.map((entry) => ({
          file_path: entry.filePath,
          file_hash: entry.fileHash,
          chunk_hashes: entry.chunkHashes,
          type: entry.type,
        })),
        modified: diff.modified.map((entry) => ({
          file_path: entry.filePath,
          file_hash: entry.fileHash,
          chunk_hashes: entry.chunkHashes,
          type: entry.type,
        })),
        deleted: diff.deleted,
        unchanged: diff.unchanged.map((entry) => ({
          file_path: entry.filePath,
          file_hash: entry.fileHash,
          chunk_hashes: entry.chunkHashes,
          type: entry.type,
        })),
      },
      files: files, // Use files directly - same structure as indexing
      batch_index: batchIndex,
    };
  }

  /**
   * Log batch payload for debugging
   */
  private logBatchPayload(payload: any, batchIndex: number): void {
    console.log(
      `📋 [${new Date().toISOString()}] Batch ${
        batchIndex + 1
      } payload summary:`
    );
    console.log(`    Project ID: ${payload.project_id}`);
    console.log(`    New Merkle Root: ${payload.merkle_tree.root}`);
    console.log(`    Files: ${payload.files.length}`);
    payload.files.forEach((file: any, fileIndex: number) => {
      console.log(
        `    File ${fileIndex + 1}: ${file.obfuscated_path}, ${
          file.chunks.length
        } chunks, type: ${file.type}`
      );
    });
  }
}
