import * as fs from "fs";
import * as path from "path";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { computeHash, obfuscatePath, getLanguage, loadIndexingConfig } from "./utils";
import { FileMetadata, Chunk } from "./models";

// Define language constants that match LangChain's actually supported languages
const LangChainSupportedLanguages = {
  PYTHON: "python",
  JS: "js",
  JAVA: "java",
  CPP: "cpp",
  GO: "go",
  RUBY: "ruby",
  PHP: "php",
  SCALA: "scala",
  HTML: "html",
  MARKDOWN: "markdown",
  LATEX: "latex",
  SOL: "sol",
  RUST: "rust",
  SWIFT: "swift",
  PROTO: "proto",
  RST: "rst",
} as const;

type LangChainLanguage =
  (typeof LangChainSupportedLanguages)[keyof typeof LangChainSupportedLanguages];

export class LangChainChunker {
  private chunkSize: number;
  private chunkOverlap: number;

  constructor() {
    const config = loadIndexingConfig();
    this.chunkSize = config.chunkSize;
    this.chunkOverlap = config.chunkOverlap;
  }

  // Map file extensions to language strings (mapping unsupported languages to similar supported ones)
  private extensionToLanguage: { [key: string]: LangChainLanguage } = {
    ".py": LangChainSupportedLanguages.PYTHON,
    ".js": LangChainSupportedLanguages.JS,
    ".ts": LangChainSupportedLanguages.JS, // TypeScript -> JavaScript
    ".jsx": LangChainSupportedLanguages.JS,
    ".tsx": LangChainSupportedLanguages.JS, // TypeScript React -> JavaScript
    ".java": LangChainSupportedLanguages.JAVA,
    ".cpp": LangChainSupportedLanguages.CPP,
    ".cxx": LangChainSupportedLanguages.CPP,
    ".cc": LangChainSupportedLanguages.CPP,
    ".c": LangChainSupportedLanguages.CPP, // C -> C++
    ".h": LangChainSupportedLanguages.CPP,
    ".hpp": LangChainSupportedLanguages.CPP,
    ".go": LangChainSupportedLanguages.GO,
    ".rb": LangChainSupportedLanguages.RUBY,
    ".php": LangChainSupportedLanguages.PHP,
    ".scala": LangChainSupportedLanguages.SCALA,
    ".html": LangChainSupportedLanguages.HTML,
    ".htm": LangChainSupportedLanguages.HTML,
    ".css": LangChainSupportedLanguages.HTML, // CSS -> HTML (similar markup)
    ".scss": LangChainSupportedLanguages.HTML,
    ".sass": LangChainSupportedLanguages.HTML,
    ".sql": LangChainSupportedLanguages.MARKDOWN, // SQL -> Markdown (fallback)
    ".md": LangChainSupportedLanguages.MARKDOWN,
    ".markdown": LangChainSupportedLanguages.MARKDOWN,
    ".tex": LangChainSupportedLanguages.LATEX,
    ".sol": LangChainSupportedLanguages.SOL,
    ".rs": LangChainSupportedLanguages.RUST,
    ".swift": LangChainSupportedLanguages.SWIFT,
    ".proto": LangChainSupportedLanguages.PROTO,
    ".rst": LangChainSupportedLanguages.RST,
    // Map unsupported languages to reasonable fallbacks
    ".cs": LangChainSupportedLanguages.JAVA, // C# -> Java (similar syntax)
    ".kt": LangChainSupportedLanguages.JAVA, // Kotlin -> Java
    ".lua": LangChainSupportedLanguages.PYTHON, // Lua -> Python (similar scripting)
    ".pl": LangChainSupportedLanguages.PYTHON, // Perl -> Python
    ".sh": LangChainSupportedLanguages.MARKDOWN, // Shell -> Markdown
    ".bash": LangChainSupportedLanguages.MARKDOWN,
    ".yml": LangChainSupportedLanguages.MARKDOWN, // YAML -> Markdown
    ".yaml": LangChainSupportedLanguages.MARKDOWN,
    ".json": LangChainSupportedLanguages.JS, // JSON -> JavaScript
    ".xml": LangChainSupportedLanguages.HTML, // XML -> HTML
  };

  async chunkFile(
    filePath: string,
    workspaceRoot: string
  ): Promise<FileMetadata | null> {
    try {
      console.log(`=== Starting LangChain chunking for: ${filePath} ===`);
      const content = fs.readFileSync(filePath, "utf-8");
      console.log(`File content length: ${content.length} characters`);

      // Skip empty files or files that are too large
      if (content.length === 0) {
        console.log(`Skipping empty file: ${filePath}`);
        return null;
      }

      if (content.length > 10 * 1024 * 1024) { // 10MB limit
        console.log(`Skipping large file (${content.length} bytes): ${filePath}`);
        return null;
      }

      // Determine language from file extension
      const fileExtension = path.extname(filePath).toLowerCase();
      const language =
        this.extensionToLanguage[fileExtension] ||
        LangChainSupportedLanguages.MARKDOWN; // Default fallback

      console.log(
        `Detected language: ${language} for extension: ${fileExtension}`
      );

      // Create language-specific splitter
      let splitter: RecursiveCharacterTextSplitter;

      try {
        splitter = RecursiveCharacterTextSplitter.fromLanguage(language, {
          chunkSize: this.chunkSize,
          chunkOverlap: this.chunkOverlap,
        });
        console.log(`Created language-specific splitter for: ${language}`);
      } catch (error) {
        console.warn(
          `Failed to create language-specific splitter for ${language}, using default:`,
          error
        );
        // Fallback to default splitter
        splitter = new RecursiveCharacterTextSplitter({
          chunkSize: this.chunkSize,
          chunkOverlap: this.chunkOverlap,
        });
      }

      // Split the content
      const documents = await splitter.createDocuments([content]);
      console.log(`Split into ${documents.length} chunks`);

      // Convert to our chunk format
      const chunks: Chunk[] = documents.map((doc, index) => {
        const chunkText = doc.pageContent;
        const chunkHash = computeHash(chunkText);

        // Calculate line range (approximate)
        const lineRange = this.calculateLineRange(content, chunkText, index);

        return {
          text: chunkText,
          chunkHash,
          lineRange,
        };
      });

      // Calculate file hash
      const fileHash = computeHash(content);
      const obfuscatedPath = obfuscatePath(filePath, workspaceRoot);
      const detectedLanguage = getLanguage(filePath);

      const fileMetadata: FileMetadata = {
        actualPath: filePath,
        obfuscatedPath,
        fileHash,
        language: detectedLanguage,
        chunks,
      };

      console.log(
        `✅ Successfully chunked file: ${filePath} into ${chunks.length} chunks`
      );

      return fileMetadata;
    } catch (error) {
      console.error(`❌ Error chunking file ${filePath}:`, error);
      return null;
    }
  }

  private calculateLineRange(
    fullContent: string,
    chunkText: string,
    chunkIndex: number
  ): { start: number; end: number } {
    try {
      // Find the chunk text in the full content
      const chunkStart = fullContent.indexOf(chunkText);
      if (chunkStart === -1) {
        // Fallback: estimate based on chunk index
        const avgChunkSize = this.chunkSize - this.chunkOverlap;
        const estimatedStart = chunkIndex * avgChunkSize;
        const startLine = fullContent.substring(0, estimatedStart).split('\n').length;
        const endLine = startLine + chunkText.split('\n').length - 1;
        return { start: Math.max(1, startLine), end: Math.max(startLine, endLine) };
      }

      // Count lines up to chunk start
      const beforeChunk = fullContent.substring(0, chunkStart);
      const startLine = beforeChunk.split('\n').length;

      // Count lines in chunk
      const chunkLines = chunkText.split('\n').length;
      const endLine = startLine + chunkLines - 1;

      return { start: Math.max(1, startLine), end: Math.max(startLine, endLine) };
    } catch (error) {
      console.warn(`Warning: Could not calculate line range for chunk ${chunkIndex}:`, error);
      // Fallback to simple estimation
      const startLine = chunkIndex * 50 + 1; // Rough estimate
      const endLine = startLine + 49;
      return { start: startLine, end: endLine };
    }
  }

  // Method to get supported file extensions
  public getSupportedExtensions(): string[] {
    return Object.keys(this.extensionToLanguage);
  }

  // Method to check if a file is supported
  public isFileSupported(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ext in this.extensionToLanguage;
  }
}
