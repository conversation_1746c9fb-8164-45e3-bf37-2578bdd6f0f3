// Data structures and interfaces for AI Coding Assistant indexing functionality

export interface Chunk {
  text: string;
  lineRange: { start: number; end: number };
  chunkHash: string;
}

export interface FileMetadata {
  actualPath: string;
  obfuscatedPath: string;
  fileHash: string;
  language: string;
  chunks: Chunk[];
}

export interface FileEntry {
  filePath: string;
  fileHash: string;
  chunkHashes: string[]; // Empty for folders
  lastModified: number;
  size: number;
  type: "file" | "folder";
  chunks: Array<{
    text: string;
    chunkHash: string;
    lineRange: { start: number; end: number };
  }>;
  obfuscatedPath: string;
  language: string;
}

export interface MerkleTree {
  root: string;
  leaves: string[];
  tree: { [key: string]: string };
  fileMap: { [key: string]: FileEntry };
  timestamp: number;
}

export interface ChangeDiff {
  added: FileEntry[];
  modified: FileEntry[];
  deleted: string[];
  unchanged: FileEntry[];
  currentTree: MerkleTree;
  newMerkleRoot: string;
  previousMerkleRoot: string;
}

export interface WorkspaceInfo {
  workspaceRoot: string;
  projectId: string;
}

// API Response interfaces
// REMOVED: IndexInitResponse - no longer needed with stateless design

export interface IndexBatchResponse {
  status: "success" | "error";
  message?: string;
  batch_index?: number;
  processed_files?: number;
}

export interface LatestMerkleTreeResponse {
  status: "success" | "error";
  merkle_tree?: MerkleTree | null;
  message?: string;
}

export interface DifferentialSyncResponse {
  status: "success" | "error";
  message?: string;
  processed_changes?: {
    added: number;
    modified: number;
    deleted: number;
  };
}

// Configuration interface
export interface IndexingConfig {
  baseUrl: string;
  batchSize: number;
  maxRetries: number;
  retryDelay: number;
  chunkSize: number;
  chunkOverlap: number;
  indexBatchEndpoint: string;
  latestMerkleEndpoint: string;
  differentialSyncEndpoint: string;
  projectSummaryEndpoint: string;
}

// Indexing status for UI updates
export interface IndexingStatus {
  isIndexing: boolean;
  currentStep: string;
  progress: number;
  totalFiles: number;
  processedFiles: number;
  error?: string;
}
