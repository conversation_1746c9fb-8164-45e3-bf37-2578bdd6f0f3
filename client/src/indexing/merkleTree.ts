import * as crypto from "crypto";
import { FileEntry, MerkleTree, ChangeDiff } from "./models";
import { computeHash } from "./utils";

export class MerkleTreeBuilder {
  private workspaceRoot: string;

  constructor(workspaceRoot: string) {
    this.workspaceRoot = workspaceRoot;
  }

  /**
   * Build a Merkle tree from file entries
   */
  buildFromFiles(files: FileEntry[]): MerkleTree {
    console.log(`🌳 Building Merkle tree from ${files.length} files`);

    // Create file map for quick lookup
    const fileMap: { [key: string]: FileEntry } = {};
    files.forEach(file => {
      fileMap[file.filePath] = file;
    });

    // Create leaves (hashes of file paths + file hashes)
    const leaves = files.map(file => {
      const leafData = `${file.filePath}:${file.fileHash}`;
      return computeHash(leafData);
    }).sort(); // Sort for consistent ordering

    // Build the tree bottom-up
    const tree = this.buildTree(leaves);
    const root = this.getRoot(tree);

    const merkleTree: MerkleTree = {
      root,
      leaves,
      tree,
      fileMap,
      timestamp: Date.now(),
    };

    console.log(`✅ Merkle tree built with root: ${root}`);
    return merkleTree;
  }

  /**
   * Build the tree structure from leaves
   */
  private buildTree(leaves: string[]): { [key: string]: string } {
    const tree: { [key: string]: string } = {};
    
    if (leaves.length === 0) {
      return tree;
    }

    // Store leaves in tree
    leaves.forEach((leaf, index) => {
      tree[`leaf_${index}`] = leaf;
    });

    let currentLevel = leaves.slice();
    let levelIndex = 0;

    // Build tree level by level
    while (currentLevel.length > 1) {
      const nextLevel: string[] = [];
      
      for (let i = 0; i < currentLevel.length; i += 2) {
        const left = currentLevel[i];
        const right = i + 1 < currentLevel.length ? currentLevel[i + 1] : left;
        
        const combined = left + right;
        const parentHash = computeHash(combined);
        
        // Store in tree with level and position info
        const nodeKey = `level_${levelIndex}_pos_${Math.floor(i / 2)}`;
        tree[nodeKey] = parentHash;
        
        nextLevel.push(parentHash);
      }
      
      currentLevel = nextLevel;
      levelIndex++;
    }

    return tree;
  }

  /**
   * Get the root hash of the tree
   */
  private getRoot(tree: { [key: string]: string }): string {
    // Find the highest level node (root)
    const keys = Object.keys(tree);
    const levelKeys = keys.filter(key => key.startsWith('level_'));
    
    if (levelKeys.length === 0) {
      // Only leaves, compute root from them
      const leafKeys = keys.filter(key => key.startsWith('leaf_'));
      if (leafKeys.length === 0) {
        return computeHash("empty");
      }
      if (leafKeys.length === 1) {
        return tree[leafKeys[0]];
      }
      // Multiple leaves, find the highest level
      const leaves = leafKeys.map(key => tree[key]);
      return this.computeRootFromLeaves(leaves);
    }

    // Find the highest level
    const levels = levelKeys.map(key => {
      const match = key.match(/level_(\d+)_pos_\d+/);
      return match ? parseInt(match[1]) : 0;
    });
    
    const maxLevel = Math.max(...levels);
    const rootKey = `level_${maxLevel}_pos_0`;
    
    return tree[rootKey] || computeHash("empty");
  }

  /**
   * Compute root from leaves directly
   */
  private computeRootFromLeaves(leaves: string[]): string {
    if (leaves.length === 0) return computeHash("empty");
    if (leaves.length === 1) return leaves[0];

    let currentLevel = leaves.slice().sort();
    
    while (currentLevel.length > 1) {
      const nextLevel: string[] = [];
      
      for (let i = 0; i < currentLevel.length; i += 2) {
        const left = currentLevel[i];
        const right = i + 1 < currentLevel.length ? currentLevel[i + 1] : left;
        const combined = left + right;
        nextLevel.push(computeHash(combined));
      }
      
      currentLevel = nextLevel;
    }
    
    return currentLevel[0];
  }

  /**
   * Detect changes between current files and previous Merkle tree
   */
  async detectChanges(
    currentFiles: FileEntry[],
    previousMerkleTree: MerkleTree | null
  ): Promise<ChangeDiff> {
    console.log(`🔍 Detecting changes...`);
    console.log(`Current files: ${currentFiles.length}`);
    console.log(`Previous tree exists: ${!!previousMerkleTree}`);

    // Build current tree
    const currentTree = this.buildFromFiles(currentFiles);

    if (!previousMerkleTree) {
      // No previous tree, everything is new
      console.log(`No previous tree found, all files are new`);
      return {
        added: currentFiles,
        modified: [],
        deleted: [],
        unchanged: [],
        currentTree,
        newMerkleRoot: currentTree.root,
        previousMerkleRoot: "",
      };
    }

    // Compare trees
    const added: FileEntry[] = [];
    const modified: FileEntry[] = [];
    const unchanged: FileEntry[] = [];
    const deleted: string[] = [];

    // Check current files against previous
    for (const currentFile of currentFiles) {
      const previousFile = previousMerkleTree.fileMap[currentFile.filePath];
      
      if (!previousFile) {
        // File is new
        added.push(currentFile);
      } else if (previousFile.fileHash !== currentFile.fileHash) {
        // File was modified
        modified.push(currentFile);
      } else {
        // File is unchanged
        unchanged.push(currentFile);
      }
    }

    // Check for deleted files
    for (const previousFilePath of Object.keys(previousMerkleTree.fileMap)) {
      const currentFile = currentFiles.find(f => f.filePath === previousFilePath);
      if (!currentFile) {
        deleted.push(previousFilePath);
      }
    }

    console.log(`📊 Change summary:`);
    console.log(`  Added: ${added.length}`);
    console.log(`  Modified: ${modified.length}`);
    console.log(`  Deleted: ${deleted.length}`);
    console.log(`  Unchanged: ${unchanged.length}`);

    return {
      added,
      modified,
      deleted,
      unchanged,
      currentTree,
      newMerkleRoot: currentTree.root,
      previousMerkleRoot: previousMerkleTree.root,
    };
  }

  /**
   * Verify the integrity of a Merkle tree
   */
  verifyTree(merkleTree: MerkleTree): boolean {
    try {
      // Rebuild tree from file map and compare roots
      const files = Object.values(merkleTree.fileMap);
      const rebuiltTree = this.buildFromFiles(files);
      
      const isValid = rebuiltTree.root === merkleTree.root;
      console.log(`🔐 Tree verification: ${isValid ? 'VALID' : 'INVALID'}`);
      
      return isValid;
    } catch (error) {
      console.error(`❌ Error verifying tree:`, error);
      return false;
    }
  }

  /**
   * Get tree statistics
   */
  getTreeStats(merkleTree: MerkleTree): {
    totalFiles: number;
    totalChunks: number;
    treeDepth: number;
    rootHash: string;
  } {
    const files = Object.values(merkleTree.fileMap);
    const totalFiles = files.length;
    const totalChunks = files.reduce((sum, file) => sum + file.chunks.length, 0);
    
    // Calculate tree depth
    const leafCount = merkleTree.leaves.length;
    const treeDepth = leafCount > 0 ? Math.ceil(Math.log2(leafCount)) + 1 : 0;

    return {
      totalFiles,
      totalChunks,
      treeDepth,
      rootHash: merkleTree.root,
    };
  }

  /**
   * Export tree to JSON string
   */
  exportTree(merkleTree: MerkleTree): string {
    return JSON.stringify(merkleTree, null, 2);
  }

  /**
   * Import tree from JSON string
   */
  importTree(jsonString: string): MerkleTree | null {
    try {
      const tree = JSON.parse(jsonString) as MerkleTree;
      
      // Validate required properties
      if (!tree.root || !tree.leaves || !tree.tree || !tree.fileMap) {
        console.error(`Invalid tree structure in JSON`);
        return null;
      }
      
      return tree;
    } catch (error) {
      console.error(`Error importing tree from JSON:`, error);
      return null;
    }
  }
}
