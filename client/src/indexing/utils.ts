import * as crypto from "crypto";
import * as path from "path";
import * as fs from "fs";
import ignore from "ignore";
import * as pako from "pako";
import * as vscode from "vscode";
import {
  FileMetadata,
  FileEntry,
  WorkspaceInfo,
  IndexingConfig,
} from "./models";

// Utility functions for hashing and obfuscation
export function computeHash(content: string): string {
  return crypto.createHash("sha256").update(content).digest("hex");
}

export function obfuscatePath(filePath: string, workspaceRoot: string): string {
  const relativePath = path.relative(workspaceRoot, filePath);
  return computeHash(relativePath);
}

export function getLanguage(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  const languageMap: { [key: string]: string } = {
    ".py": "python",
    ".js": "javascript",
    ".ts": "typescript",
    ".jsx": "javascript",
    ".tsx": "typescript",
    ".java": "java",
    ".cpp": "cpp",
    ".cxx": "cpp",
    ".cc": "cpp",
    ".c": "c",
    ".h": "cpp",
    ".hpp": "cpp",
    ".go": "go",
    ".rb": "ruby",
    ".php": "php",
    ".scala": "scala",
    ".html": "html",
    ".htm": "html",
    ".css": "css",
    ".scss": "css",
    ".sass": "css",
    ".sql": "sql",
    ".md": "markdown",
    ".markdown": "markdown",
    ".tex": "latex",
    ".sol": "solidity",
    ".rs": "rust",
    ".swift": "swift",
    ".proto": "protobuf",
    ".rst": "restructuredtext",
    ".cs": "csharp",
    ".kt": "kotlin",
    ".lua": "lua",
    ".pl": "perl",
    ".sh": "shell",
    ".bash": "shell",
    ".yml": "yaml",
    ".yaml": "yaml",
    ".json": "json",
    ".xml": "xml",
  };
  return languageMap[ext] || "text";
}

// File filtering and ignore patterns
export function getIgnoreFilter(
  workspaceRoot: string
): (filePath: string) => boolean {
  const ig = ignore();
  const gitignorePath = path.join(workspaceRoot, ".gitignore");
  const cursorignorePath = path.join(workspaceRoot, ".cursorignore");
  const envIgnore = [
    "node_modules",
    "dist",
    "build",
    ".git",
    ".env",
    ".vscode",
    "out",
    "target",
    "__pycache__",
    "*.pyc",
    ".DS_Store",
    "Thumbs.db",
    "*.log",
    "*.tmp",
    "*.temp",
  ];

  if (fs.existsSync(gitignorePath)) {
    ig.add(fs.readFileSync(gitignorePath, "utf-8"));
  }
  if (fs.existsSync(cursorignorePath)) {
    ig.add(fs.readFileSync(cursorignorePath, "utf-8"));
  }
  ig.add(envIgnore);

  return (filePath: string) => {
    const relativePath = path.relative(workspaceRoot, filePath);
    return !ig.ignores(relativePath);
  };
}

// Compression utilities
export function compressPayload(payload: any): string {
  const jsonString = JSON.stringify(payload);
  const compressed = pako.gzip(jsonString);
  return Buffer.from(compressed).toString("base64");
}

// Workspace information
export function getWorkspaceInfo(): WorkspaceInfo | null {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders) {
    vscode.window.showErrorMessage("No workspace folder open");
    return null;
  }

  const workspaceRoot = workspaceFolders[0].uri.fsPath;
  const projectId = path.basename(workspaceRoot);
  return { workspaceRoot, projectId };
}

// Configuration loading
export function loadIndexingConfig(): IndexingConfig {
  // Build base URL from host and port
  const host = process.env.RECODE_SERVER_HOST || "127.0.0.1";
  const port = process.env.RECODE_SERVER_PORT || "8000";
  const baseUrl = `http://${host}:${port}`;

  return {
    baseUrl,
    batchSize: parseInt(process.env.RECODE_BATCH_SIZE || "10"),
    maxRetries: parseInt(process.env.RECODE_MAX_RETRIES || "3"),
    retryDelay: parseInt(process.env.RECODE_RETRY_DELAY || "1000"),
    chunkSize: parseInt(process.env.RECODE_CHUNK_SIZE || "2000"),
    chunkOverlap: parseInt(process.env.RECODE_CHUNK_OVERLAP || "200"),
    indexBatchEndpoint:
      process.env.RECODE_INDEX_BATCH_ENDPOINT || "/index/batch",
    latestMerkleEndpoint:
      process.env.RECODE_INDEX_LATEST_MERKLE_ENDPOINT ||
      "/index/latest_merkle_tree",
    differentialSyncEndpoint:
      process.env.RECODE_SYNC_DIFFERENTIAL_ENDPOINT || "/sync/differential",
    projectSummaryEndpoint:
      process.env.RECODE_PROJECT_SUMMARY_ENDPOINT || "/project/summary",
  };
}

// Get differential sync interval in milliseconds
export function getDiffSyncInterval(): number {
  const minutes = parseInt(
    process.env.RECODE_DIFF_SYNC_INTERVAL_MINUTES || "6"
  );
  return minutes * 60 * 1000; // Convert to milliseconds
}

// Batching utilities
export function createBatches<T>(items: T[], batchSize: number): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }
  return batches;
}

// Logging utilities
export function logBatchBreakdown<T>(batches: T[][]): void {
  console.log(`📊 Batch breakdown:`);
  batches.forEach((batch, index) => {
    console.log(`  Batch ${index + 1}: ${batch.length} items`);
  });
}

export function logFileAndChunkDetails(files: FileEntry[]): void {
  const totalChunks = files.reduce(
    (sum, file) => sum + (file.chunks ? file.chunks.length : 0),
    0
  );
  console.log(`📋 File details:`);
  console.log(`  Total files: ${files.length}`);
  console.log(`  Total chunks: ${totalChunks}`);

  files.slice(0, 5).forEach((file, index) => {
    console.log(
      `  ${index + 1}. ${file.obfuscatedPath} (${
        file.chunks.length
      } chunks) - ${file.type}`
    );
  });

  if (files.length > 5) {
    console.log(`  ... and ${files.length - 5} more files`);
  }
}

// File system utilities
export function isValidFile(filePath: string): boolean {
  try {
    const stats = fs.statSync(filePath);
    return stats.isFile() && stats.size > 0 && stats.size < 10 * 1024 * 1024; // Max 10MB
  } catch {
    return false;
  }
}

export function getFileStats(
  filePath: string
): { size: number; lastModified: number } | null {
  try {
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      lastModified: stats.mtime.getTime(),
    };
  } catch {
    return null;
  }
}

// Folder entry creation
export function createFolderEntries(
  files: FileMetadata[],
  workspaceRoot: string
): FileEntry[] {
  const folderEntries: FileEntry[] = [];
  const addedFolders = new Set<string>();

  for (const file of files) {
    let currentDir = path.dirname(file.actualPath);

    while (
      currentDir !== workspaceRoot &&
      currentDir !== "." &&
      !addedFolders.has(currentDir)
    ) {
      folderEntries.push({
        filePath: currentDir,
        fileHash: "",
        chunkHashes: [],
        lastModified: Date.now(),
        size: 0,
        type: "folder" as const,
        chunks: [],
        obfuscatedPath: obfuscatePath(currentDir, workspaceRoot),
        language: "",
      });

      addedFolders.add(currentDir);
      currentDir = path.dirname(currentDir);
    }
  }

  return folderEntries;
}

// Error handling utilities
export function handleIndexingError(error: any, context: string): void {
  console.error(`❌ [${new Date().toISOString()}] Error in ${context}:`, error);
  vscode.window.showErrorMessage(
    `AI Assistant indexing error: ${error.message || error}`
  );
}

export function showIndexingProgress(message: string): void {
  console.log(`🔄 [${new Date().toISOString()}] ${message}`);
}

export function showIndexingSuccess(message: string): void {
  console.log(`✅ [${new Date().toISOString()}] ${message}`);
  // Don't show VS Code notification here - let the webview handle it
}

// File scanning functionality
export async function scanFiles(
  workspaceRoot: string,
  chunker: any // Will be properly typed when chunker is implemented
): Promise<FileMetadata[]> {
  const filesWithPaths: FileMetadata[] = [];
  const ignoreFilter = getIgnoreFilter(workspaceRoot);

  const scanDir = async (dir: string) => {
    try {
      const entries = await fs.promises.readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (!ignoreFilter(fullPath)) continue;

        if (entry.isDirectory()) {
          await scanDir(fullPath);
        } else if (entry.isFile() && isValidFile(fullPath)) {
          const file = await chunker.chunkFile(fullPath, workspaceRoot);
          if (file) {
            // Add actualPath to the file metadata
            filesWithPaths.push({
              ...file,
              actualPath: fullPath,
            });
          }
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}:`, error);
    }
  };

  await scanDir(workspaceRoot);
  return filesWithPaths;
}

// File entry creation utilities
export async function createFileEntries(
  fileMetadata: FileMetadata[],
  workspaceRoot: string,
  merkleTreeBuilder: any // Will be properly typed when merkleTree is implemented
): Promise<FileEntry[]> {
  return fileMetadata.map((file) => ({
    filePath: file.actualPath,
    fileHash: file.fileHash,
    chunkHashes: file.chunks.map((chunk) => chunk.chunkHash),
    lastModified: Date.now(),
    size: file.chunks.reduce((total, chunk) => total + chunk.text.length, 0),
    type: "file" as const,
    chunks: file.chunks.map((chunk) => ({
      text: chunk.text,
      chunkHash: chunk.chunkHash,
      lineRange: chunk.lineRange,
    })),
    obfuscatedPath: file.obfuscatedPath,
    language: file.language,
  }));
}
