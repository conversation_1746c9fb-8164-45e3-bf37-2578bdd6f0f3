/**
 * Language Feedback Manager - Detects project language and provides compilation feedback
 * Extensible system for different programming languages
 */
import * as vscode from "vscode";
import * as path from "path";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export interface CompilationResult {
  success: boolean;
  language: string;
  errors: CompilationError[];
  warnings: CompilationWarning[];
  output: string;
  timestamp: number;
  requiresUserAction?: boolean;
  userActionMessage?: string;
  userActionCommands?: UserActionCommand[];
}

export interface UserActionCommand {
  description: string;
  command: string;
  platform: "darwin" | "linux" | "win32" | "all";
}

export interface CompilationError {
  file: string;
  line: number;
  column: number;
  message: string;
  severity: "error" | "warning";
}

export interface CompilationWarning {
  file: string;
  line: number;
  column: number;
  message: string;
}

export interface LanguageDetectionResult {
  language: string;
  confidence: number;
  buildFiles: string[];
  sourceFiles: string[];
}

export class LanguageFeedbackManager {
  private workspaceRoot: string;
  private supportedLanguages = [
    "java",
    "typescript",
    "javascript",
    "python",
    "csharp",
  ];

  constructor(workspaceRoot: string) {
    this.workspaceRoot = workspaceRoot;
  }

  /**
   * Detect the primary programming language of the project
   */
  async detectProjectLanguage(): Promise<LanguageDetectionResult> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      throw new Error("No workspace found");
    }

    const workspaceUri = workspaceFolders[0].uri;

    // Check for build files and project indicators
    const languageIndicators = {
      java: {
        buildFiles: [
          "pom.xml",
          "build.gradle",
          "build.gradle.kts",
          "gradle.properties",
        ],
        sourceExtensions: [".java"],
        directories: ["src/main/java", "src/test/java"],
      },
      typescript: {
        buildFiles: ["tsconfig.json", "package.json"],
        sourceExtensions: [".ts", ".tsx"],
        directories: ["src", "lib"],
      },
      javascript: {
        buildFiles: ["package.json", "webpack.config.js", "rollup.config.js"],
        sourceExtensions: [".js", ".jsx"],
        directories: ["src", "lib"],
      },
      python: {
        buildFiles: [
          "requirements.txt",
          "setup.py",
          "pyproject.toml",
          "Pipfile",
        ],
        sourceExtensions: [".py"],
        directories: ["src", "lib"],
      },
      csharp: {
        buildFiles: [".csproj", ".sln", "project.json"],
        sourceExtensions: [".cs"],
        directories: ["src", "lib"],
      },
    };

    let bestMatch: LanguageDetectionResult = {
      language: "unknown",
      confidence: 0,
      buildFiles: [],
      sourceFiles: [],
    };

    for (const [language, indicators] of Object.entries(languageIndicators)) {
      let confidence = 0;
      const foundBuildFiles: string[] = [];
      const foundSourceFiles: string[] = [];

      // Check for build files
      for (const buildFile of indicators.buildFiles) {
        try {
          const buildFileUri = vscode.Uri.joinPath(workspaceUri, buildFile);
          await vscode.workspace.fs.stat(buildFileUri);
          foundBuildFiles.push(buildFile);
          confidence += 30; // High weight for build files
        } catch {
          // File doesn't exist
        }
      }

      // Check for source files
      const files = await vscode.workspace.findFiles(
        `**/*{${indicators.sourceExtensions.join(",")}}`,
        "**/node_modules/**"
      );

      if (files.length > 0) {
        foundSourceFiles.push(
          ...files.map((f) => vscode.workspace.asRelativePath(f))
        );
        confidence += Math.min(files.length * 2, 50); // Weight by number of source files
      }

      if (confidence > bestMatch.confidence) {
        bestMatch = {
          language,
          confidence,
          buildFiles: foundBuildFiles,
          sourceFiles: foundSourceFiles.slice(0, 10), // Limit to first 10 files
        };
      }
    }

    return bestMatch;
  }

  /**
   * Compile and get feedback for the detected language
   */
  async getCompilationFeedback(
    changedFiles: string[] = []
  ): Promise<CompilationResult> {
    // Clean file paths by removing project name prefix if present
    const cleanedFiles = this.cleanFilePaths(changedFiles);
    console.log(`📁 Original files: ${changedFiles.join(", ")}`);
    console.log(`📁 Cleaned files: ${cleanedFiles.join(", ")}`);

    const languageInfo = await this.detectProjectLanguage();

    console.log(
      `🔍 Detected language: ${languageInfo.language} (confidence: ${languageInfo.confidence}%)`
    );

    switch (languageInfo.language) {
      case "java":
        return await this.compileJavaProject(cleanedFiles);
      case "typescript":
        return await this.compileTypeScriptProject(cleanedFiles);
      case "javascript":
        return await this.lintJavaScriptProject(cleanedFiles);
      case "python":
        return await this.checkPythonProject(cleanedFiles);
      case "csharp":
        return await this.compileCSharpProject(cleanedFiles);
      default:
        return {
          success: true,
          language: languageInfo.language,
          errors: [],
          warnings: [],
          output: `Language ${languageInfo.language} is not supported for compilation feedback yet.`,
          timestamp: Date.now(),
        };
    }
  }

  /**
   * Clean up long Java stack traces and extract the most relevant parts
   */
  private cleanJavaErrorMessage(message: string): string {
    // If message is short, return as-is
    if (message.length <= 200) {
      return message;
    }

    const lines = message.split("\n");
    const cleanedLines: string[] = [];
    let foundCausedBy = false;
    let lineCount = 0;
    const maxLines = 10; // Limit to 10 most relevant lines

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines
      if (!trimmedLine) continue;

      // Always include "Caused by" lines and the lines immediately following them
      if (trimmedLine.toLowerCase().includes("caused by")) {
        foundCausedBy = true;
        cleanedLines.push(trimmedLine);
        lineCount++;
        continue;
      }

      // If we found "Caused by", include the next few lines
      if (foundCausedBy && lineCount < maxLines) {
        cleanedLines.push(trimmedLine);
        lineCount++;
        continue;
      }

      // Include compilation error lines (file:line format)
      if (
        trimmedLine.includes(".java:") &&
        (trimmedLine.includes("error:") || trimmedLine.includes("Error:"))
      ) {
        cleanedLines.push(trimmedLine);
        lineCount++;
        continue;
      }

      // Include important error keywords
      if (
        trimmedLine.toLowerCase().includes("error:") ||
        trimmedLine.toLowerCase().includes("exception:") ||
        trimmedLine.toLowerCase().includes("cannot find symbol") ||
        trimmedLine.toLowerCase().includes("cannot resolve") ||
        trimmedLine.toLowerCase().includes("incompatible types")
      ) {
        cleanedLines.push(trimmedLine);
        lineCount++;
        continue;
      }

      // Stop if we have enough lines
      if (lineCount >= maxLines) {
        break;
      }
    }

    // If we didn't find anything useful, take the first few lines
    if (cleanedLines.length === 0) {
      return lines.slice(0, 3).join("\n");
    }

    let result = cleanedLines.join("\n");

    // If still too long, truncate
    if (result.length > 500) {
      result = result.substring(0, 500) + "... (truncated)";
    }

    return result;
  }

  /**
   * Check if a command is available in the system
   */
  private async isCommandAvailable(command: string): Promise<boolean> {
    try {
      const { stdout, stderr } = await execAsync(`which ${command}`, {
        cwd: this.workspaceRoot,
        timeout: 5000,
      });
      return stdout.trim().length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Compile Java project using Maven or Gradle
   */
  private async compileJavaProject(
    changedFiles: string[]
  ): Promise<CompilationResult> {
    console.log("☕ Compiling Java project...");

    try {
      // Check if Maven or Gradle project
      const hasMaven = await this.fileExists("pom.xml");
      const hasGradle =
        (await this.fileExists("build.gradle")) ||
        (await this.fileExists("build.gradle.kts"));

      let compileCommand: string;
      let parseOutput: (output: string) => {
        errors: CompilationError[];
        warnings: CompilationWarning[];
      };

      if (hasMaven) {
        // Check if Maven is installed
        const isMavenAvailable = await this.isCommandAvailable("mvn");
        if (!isMavenAvailable) {
          console.log("❌ Maven not found, prompting user to install...");
          return {
            success: false,
            language: "java",
            errors: [
              {
                file: "pom.xml",
                line: 1,
                column: 1,
                message:
                  "Maven (mvn) command not found. Please install Maven to compile this Spring Boot project.",
                severity: "error" as const,
              },
            ],
            warnings: [],
            output:
              "Maven installation required for Spring Boot project compilation.",
            timestamp: Date.now(),
            requiresUserAction: true,
            userActionMessage:
              "Maven is required for this Spring Boot project. Would you like to install it?",
            userActionCommands: [
              {
                description: "Install Maven (macOS with Homebrew)",
                command: "brew install maven",
                platform: "darwin",
              },
              {
                description: "Install Maven (Ubuntu/Debian)",
                command: "sudo apt-get update && sudo apt-get install maven",
                platform: "linux",
              },
              {
                description: "Check Maven installation",
                command: "mvn --version",
                platform: "all",
              },
            ],
          };
        }

        compileCommand = "mvn compile -q";
        console.log(`☕ Using Maven to compile entire project`);
        parseOutput = this.parseMavenOutput.bind(this);
      } else if (hasGradle) {
        // Check if Gradle wrapper exists, otherwise check for gradle command
        const hasGradleWrapper = await this.fileExists("gradlew");
        if (!hasGradleWrapper) {
          const isGradleAvailable = await this.isCommandAvailable("gradle");
          if (!isGradleAvailable) {
            console.log("❌ Gradle not found, prompting user to install...");
            return {
              success: false,
              language: "java",
              errors: [
                {
                  file: "build.gradle",
                  line: 1,
                  column: 1,
                  message:
                    "Gradle command not found and no Gradle wrapper (gradlew) available.",
                  severity: "error" as const,
                },
              ],
              warnings: [],
              output:
                "Gradle installation required for Spring Boot project compilation.",
              timestamp: Date.now(),
              requiresUserAction: true,
              userActionMessage:
                "Gradle is required for this Spring Boot project. Would you like to install it?",
              userActionCommands: [
                {
                  description: "Install Gradle (macOS with Homebrew)",
                  command: "brew install gradle",
                  platform: "darwin",
                },
                {
                  description: "Install Gradle (Ubuntu/Debian)",
                  command: "sudo apt-get update && sudo apt-get install gradle",
                  platform: "linux",
                },
                {
                  description: "Check Gradle installation",
                  command: "gradle --version",
                  platform: "all",
                },
              ],
            };
          }
          compileCommand = "gradle compileJava --console=plain";
        } else {
          compileCommand = "./gradlew compileJava --console=plain";
        }

        console.log(`☕ Using Gradle to compile entire project`);
        parseOutput = this.parseGradleOutput.bind(this);
      } else {
        // Fallback to javac for simple core Java projects
        console.log(
          `☕ No Maven/Gradle found, using javac for simple Java project`
        );

        // Find all Java files in the project
        const files = await vscode.workspace.findFiles(
          "**/*.java",
          "**/target/**"
        );

        const allJavaFiles = files.map((f) =>
          vscode.workspace.asRelativePath(f)
        );
        console.log(`☕ Found ${allJavaFiles.length} Java files in project`);

        if (allJavaFiles.length === 0) {
          return {
            success: true,
            language: "java",
            errors: [],
            warnings: [],
            output: "No Java files found to compile",
            timestamp: Date.now(),
          };
        }

        // For simple Java projects, try different compilation strategies
        if (allJavaFiles.length === 1) {
          // Single file - compile directly
          compileCommand = `javac -cp . ${allJavaFiles[0]}`;
          console.log(`☕ Single file compilation: ${compileCommand}`);
        } else if (allJavaFiles.length <= 10) {
          // Small project - compile all files together
          compileCommand = `javac -cp . ${allJavaFiles.join(" ")}`;
          console.log(
            `☕ Small project compilation (${allJavaFiles.length} files): ${compileCommand}`
          );
        } else {
          // Larger project - try to find source directory and compile from there
          const srcDirs = this.findSourceDirectories(allJavaFiles);
          if (srcDirs.length > 0) {
            // Compile from source directories
            const srcPatterns = srcDirs
              .map((dir) => `${dir}/**/*.java`)
              .join(" ");
            compileCommand = `find ${srcDirs.join(
              " "
            )} -name "*.java" -exec javac -cp . {} +`;
            console.log(`☕ Source directory compilation: ${compileCommand}`);
          } else {
            // Fallback - compile all files
            compileCommand = `javac -cp . ${allJavaFiles.join(" ")}`;
            console.log(
              `☕ All files compilation (${allJavaFiles.length} files): ${compileCommand}`
            );
          }
        }

        console.log(
          `☕ Changed files that triggered compilation: ${changedFiles.join(
            ", "
          )}`
        );
        parseOutput = this.parseJavacOutput.bind(this);
      }

      const { stdout, stderr } = await execAsync(compileCommand, {
        cwd: this.workspaceRoot,
        timeout: 30000, // 30 second timeout
      });

      const output = stdout + stderr;
      console.log(`☕ Compilation output (stdout): ${stdout}`);
      console.log(`☕ Compilation output (stderr): ${stderr}`);
      console.log(`☕ Combined output: ${output}`);

      const { errors, warnings } = parseOutput(output);
      console.log(
        `☕ Parsed errors: ${errors.length}, warnings: ${warnings.length}`
      );

      // For successful Maven/Gradle compilation, suggest testing commands
      const compilationResult: CompilationResult = {
        success: errors.length === 0,
        language: "java",
        errors,
        warnings,
        output,
        timestamp: Date.now(),
      };

      // Add testing suggestions for successful Spring Boot builds
      if (errors.length === 0 && (hasMaven || hasGradle)) {
        const testCommands: UserActionCommand[] = [];

        if (hasMaven) {
          testCommands.push(
            {
              description: "Run full Maven build with tests",
              command: "mvn clean install",
              platform: "all",
            },
            {
              description: "Run Maven tests only",
              command: "mvn test",
              platform: "all",
            },
            {
              description: "Start Spring Boot application",
              command: "mvn spring-boot:run",
              platform: "all",
            }
          );
        } else if (hasGradle) {
          // Check for Gradle wrapper again for testing commands
          const hasGradleWrapperForTest = await this.fileExists("gradlew");
          testCommands.push(
            {
              description: "Run full Gradle build with tests",
              command: hasGradleWrapperForTest
                ? "./gradlew build"
                : "gradle build",
              platform: "all",
            },
            {
              description: "Run Gradle tests only",
              command: hasGradleWrapperForTest
                ? "./gradlew test"
                : "gradle test",
              platform: "all",
            },
            {
              description: "Start Spring Boot application",
              command: hasGradleWrapperForTest
                ? "./gradlew bootRun"
                : "gradle bootRun",
              platform: "all",
            }
          );
        }

        compilationResult.userActionMessage =
          "✅ Compilation successful! You can now test your Spring Boot application:";
        compilationResult.userActionCommands = testCommands;
      }

      return compilationResult;
    } catch (error: any) {
      console.error("❌ Java compilation failed:", error);

      // Parse error output for compilation errors using the correct parser
      const errorOutput = error.stdout + error.stderr;
      console.log(`☕ Error output to parse: ${errorOutput}`);

      // Determine the correct parser based on the compilation method used
      let errors: CompilationError[] = [];
      let warnings: CompilationWarning[] = [];

      // Re-check for Maven/Gradle in catch block
      const hasMavenInCatch = await this.fileExists("pom.xml");
      const hasGradleInCatch =
        (await this.fileExists("build.gradle")) ||
        (await this.fileExists("build.gradle.kts"));

      if (hasMavenInCatch) {
        const result = this.parseMavenOutput(errorOutput);
        errors = result.errors;
        warnings = result.warnings;
      } else if (hasGradleInCatch) {
        const result = this.parseGradleOutput(errorOutput);
        errors = result.errors;
        warnings = result.warnings;
      } else {
        const result = this.parseJavacOutput(errorOutput);
        errors = result.errors;
        warnings = result.warnings;
      }

      console.log(
        `☕ Parsed ${errors.length} errors and ${warnings.length} warnings from error output`
      );

      return {
        success: false,
        language: "java",
        errors,
        warnings,
        output: errorOutput,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Parse Maven compilation output
   */
  private parseMavenOutput(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");

    // Check for Maven command not found error first
    const commandNotFoundPattern =
      /mvn:\s*command\s*not\s*found|\/bin\/sh:\s*mvn:\s*command\s*not\s*found/i;
    if (output.match(commandNotFoundPattern)) {
      errors.push({
        file: "pom.xml",
        line: 1,
        column: 1,
        message:
          "Maven (mvn) command not found. Please install Maven to compile this Spring Boot project. You can install it using: brew install maven (macOS) or apt-get install maven (Ubuntu) or download from https://maven.apache.org/",
        severity: "error",
      });
      return { errors, warnings };
    }

    // Check for Maven build failures (general)
    const buildFailurePattern = /BUILD\s+FAILURE/i;
    if (output.match(buildFailurePattern)) {
      // Look for specific error messages in Maven output
      const mavenErrorPatterns = [
        /Failed to execute goal.*maven-compiler-plugin.*compile.*compilation failure/i,
        /Non-resolvable parent POM/i,
        /Could not find artifact/i,
        /Unknown packaging:/i,
        /Project build error:/i,
      ];

      let foundSpecificError = false;
      for (const line of lines) {
        for (const pattern of mavenErrorPatterns) {
          if (pattern.test(line)) {
            errors.push({
              file: "pom.xml",
              line: 1,
              column: 1,
              message: `Maven build error: ${line.trim()}`,
              severity: "error",
            });
            foundSpecificError = true;
            break;
          }
        }
      }

      // If no specific error found but BUILD FAILURE detected, add generic error
      if (!foundSpecificError) {
        errors.push({
          file: "pom.xml",
          line: 1,
          column: 1,
          message:
            "Maven build failed. Check pom.xml configuration - missing parent, invalid dependencies, or incorrect project structure.",
          severity: "error",
        });
      }
    }

    for (const line of lines) {
      // Maven error pattern: [ERROR] /path/to/file.java:[line,column] error message
      const errorMatch = line.match(
        /\[ERROR\]\s+(.+\.java):\[(\d+),(\d+)\]\s+(.+)/
      );
      if (errorMatch) {
        errors.push({
          file: path.basename(errorMatch[1]),
          line: parseInt(errorMatch[2]),
          column: parseInt(errorMatch[3]),
          message: this.cleanJavaErrorMessage(errorMatch[4]),
          severity: "error",
        });
        continue;
      }

      // Maven warning pattern: [WARNING] /path/to/file.java:[line,column] warning message
      const warningMatch = line.match(
        /\[WARNING\]\s+(.+\.java):\[(\d+),(\d+)\]\s+(.+)/
      );
      if (warningMatch) {
        warnings.push({
          file: path.basename(warningMatch[1]),
          line: parseInt(warningMatch[2]),
          column: parseInt(warningMatch[3]),
          message: this.cleanJavaErrorMessage(warningMatch[4]),
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Parse Gradle compilation output
   */
  private parseGradleOutput(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");

    for (const line of lines) {
      // Gradle error pattern: /path/to/file.java:line: error: message
      const errorMatch = line.match(/(.+\.java):(\d+):\s+error:\s+(.+)/);
      if (errorMatch) {
        errors.push({
          file: path.basename(errorMatch[1]),
          line: parseInt(errorMatch[2]),
          column: 0,
          message: this.cleanJavaErrorMessage(errorMatch[3]),
          severity: "error",
        });
        continue;
      }

      // Gradle warning pattern: /path/to/file.java:line: warning: message
      const warningMatch = line.match(/(.+\.java):(\d+):\s+warning:\s+(.+)/);
      if (warningMatch) {
        warnings.push({
          file: path.basename(warningMatch[1]),
          line: parseInt(warningMatch[2]),
          column: 0,
          message: this.cleanJavaErrorMessage(warningMatch[3]),
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Parse javac compilation output
   */
  private parseJavacOutput(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");
    console.log(`🔍 Parsing javac output with ${lines.length} lines`);

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // javac error pattern: file.java:line: error: message
      const errorMatch = trimmedLine.match(/(.+\.java):(\d+):\s+error:\s+(.+)/);
      if (errorMatch) {
        console.log(`✅ Matched javac error: ${trimmedLine}`);
        errors.push({
          file: path.basename(errorMatch[1]),
          line: parseInt(errorMatch[2]),
          column: 0,
          message: this.cleanJavaErrorMessage(errorMatch[3]),
          severity: "error",
        });
        continue;
      }

      // javac warning pattern: file.java:line: warning: message
      const warningMatch = line.match(/(.+\.java):(\d+):\s+warning:\s+(.+)/);
      if (warningMatch) {
        warnings.push({
          file: path.basename(warningMatch[1]),
          line: parseInt(warningMatch[2]),
          column: 0,
          message: this.cleanJavaErrorMessage(warningMatch[3]),
        });
        continue;
      }

      // General javac error patterns (file not found, etc.)
      const generalErrorMatch = line.match(/^error:\s+(.+)/);
      if (generalErrorMatch) {
        // Extract file name if present in the error message
        const fileMatch = generalErrorMatch[1].match(/file not found:\s+(.+)/);
        const fileName = fileMatch ? path.basename(fileMatch[1]) : "unknown";

        errors.push({
          file: fileName,
          line: 0,
          column: 0,
          message: generalErrorMatch[1],
          severity: "error",
        });
        continue;
      }

      // Catch any line that contains "error" but doesn't match above patterns
      // Exclude summary lines like "1 error." or "2 errors."
      if (
        line.toLowerCase().includes("error") &&
        line.trim().length > 0 &&
        !line.includes("Usage:") &&
        !line.includes("use --help") &&
        !line.match(/^\d+\s+errors?\.$/) && // Exclude "1 error." or "2 errors."
        !line.match(/^\d+\s+errors?\s*$/) && // Exclude "1 error" or "2 errors"
        !line.includes("^") && // Exclude pointer lines like "           ^"
        !line.match(/^\s*symbol:\s+/) && // Exclude "  symbol: variable input"
        !line.match(/^\s*location:\s+/) // Exclude "  location: class StrUtils"
      ) {
        console.log(`⚠️ Catch-all error pattern matched: ${line.trim()}`);
        errors.push({
          file: "unknown",
          line: 0,
          column: 0,
          message: line.trim(),
          severity: "error",
        });
      }
    }

    console.log(
      `🔍 Final javac parsing result: ${errors.length} errors, ${warnings.length} warnings`
    );
    if (errors.length > 0) {
      console.log(`🔍 First few errors:`, errors.slice(0, 3));
    }

    return { errors, warnings };
  }

  /**
   * Compile TypeScript project
   */
  private async compileTypeScriptProject(
    changedFiles: string[]
  ): Promise<CompilationResult> {
    console.log("🔷 Compiling TypeScript project...");

    try {
      const { stdout, stderr } = await execAsync("npx tsc --noEmit", {
        cwd: this.workspaceRoot,
        timeout: 30000,
      });

      const output = stdout + stderr;
      const { errors, warnings } = this.parseTypeScriptOutput(output);

      return {
        success: errors.length === 0,
        language: "typescript",
        errors,
        warnings,
        output,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      const errorOutput = error.stdout + error.stderr;
      const { errors, warnings } = this.parseTypeScriptOutput(errorOutput);

      return {
        success: false,
        language: "typescript",
        errors,
        warnings,
        output: errorOutput,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Parse TypeScript compilation output
   */
  private parseTypeScriptOutput(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");

    for (const line of lines) {
      // TypeScript error pattern: file.ts(line,column): error TS####: message
      const errorMatch = line.match(
        /(.+\.tsx?)\((\d+),(\d+)\):\s+error\s+TS\d+:\s+(.+)/
      );
      if (errorMatch) {
        errors.push({
          file: path.basename(errorMatch[1]),
          line: parseInt(errorMatch[2]),
          column: parseInt(errorMatch[3]),
          message: errorMatch[4],
          severity: "error",
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Lint JavaScript project
   */
  private async lintJavaScriptProject(
    changedFiles: string[]
  ): Promise<CompilationResult> {
    console.log("📝 Linting JavaScript project...");

    try {
      // Try ESLint first
      const { stdout, stderr } = await execAsync("npx eslint . --format=json", {
        cwd: this.workspaceRoot,
        timeout: 30000,
      });

      const results = JSON.parse(stdout);
      const errors: CompilationError[] = [];
      const warnings: CompilationWarning[] = [];

      for (const result of results) {
        for (const message of result.messages) {
          const item = {
            file: path.basename(result.filePath),
            line: message.line || 0,
            column: message.column || 0,
            message: message.message,
          };

          if (message.severity === 2) {
            errors.push({ ...item, severity: "error" as const });
          } else {
            warnings.push(item);
          }
        }
      }

      return {
        success: errors.length === 0,
        language: "javascript",
        errors,
        warnings,
        output: `Linted ${results.length} files`,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      return {
        success: true, // Don't fail if ESLint is not configured
        language: "javascript",
        errors: [],
        warnings: [],
        output: "ESLint not configured or failed to run",
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Check Python project
   */
  private async checkPythonProject(
    changedFiles: string[]
  ): Promise<CompilationResult> {
    console.log("🐍 Checking Python project...");

    try {
      // Use flake8 for Python linting
      const { stdout, stderr } = await execAsync(
        "python -m flake8 . --format=json",
        {
          cwd: this.workspaceRoot,
          timeout: 30000,
        }
      );

      // flake8 doesn't output JSON by default, so we'll parse the standard format
      const { errors, warnings } = this.parseFlake8Output(stdout + stderr);

      return {
        success: errors.length === 0,
        language: "python",
        errors,
        warnings,
        output: stdout + stderr,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      return {
        success: true, // Don't fail if flake8 is not installed
        language: "python",
        errors: [],
        warnings: [],
        output: "flake8 not installed or failed to run",
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Parse flake8 output
   */
  private parseFlake8Output(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");

    for (const line of lines) {
      // flake8 pattern: file.py:line:column: code message
      const match = line.match(/(.+\.py):(\d+):(\d+):\s+([EW]\d+)\s+(.+)/);
      if (match) {
        const item = {
          file: path.basename(match[1]),
          line: parseInt(match[2]),
          column: parseInt(match[3]),
          message: `${match[4]}: ${match[5]}`,
        };

        if (match[4].startsWith("E")) {
          errors.push({ ...item, severity: "error" as const });
        } else {
          warnings.push(item);
        }
      }
    }

    return { errors, warnings };
  }

  /**
   * Compile C# project
   */
  private async compileCSharpProject(
    changedFiles: string[]
  ): Promise<CompilationResult> {
    console.log("🔷 Compiling C# project...");

    try {
      const { stdout, stderr } = await execAsync(
        "dotnet build --verbosity quiet",
        {
          cwd: this.workspaceRoot,
          timeout: 30000,
        }
      );

      const output = stdout + stderr;
      const { errors, warnings } = this.parseDotNetOutput(output);

      return {
        success: errors.length === 0,
        language: "csharp",
        errors,
        warnings,
        output,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      const errorOutput = error.stdout + error.stderr;
      const { errors, warnings } = this.parseDotNetOutput(errorOutput);

      return {
        success: false,
        language: "csharp",
        errors,
        warnings,
        output: errorOutput,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Parse .NET compilation output
   */
  private parseDotNetOutput(output: string): {
    errors: CompilationError[];
    warnings: CompilationWarning[];
  } {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    const lines = output.split("\n");

    for (const line of lines) {
      // .NET error pattern: file.cs(line,column): error CS####: message
      const errorMatch = line.match(
        /(.+\.cs)\((\d+),(\d+)\):\s+error\s+CS\d+:\s+(.+)/
      );
      if (errorMatch) {
        errors.push({
          file: path.basename(errorMatch[1]),
          line: parseInt(errorMatch[2]),
          column: parseInt(errorMatch[3]),
          message: errorMatch[4],
          severity: "error",
        });
        continue;
      }

      // .NET warning pattern: file.cs(line,column): warning CS####: message
      const warningMatch = line.match(
        /(.+\.cs)\((\d+),(\d+)\):\s+warning\s+CS\d+:\s+(.+)/
      );
      if (warningMatch) {
        warnings.push({
          file: path.basename(warningMatch[1]),
          line: parseInt(warningMatch[2]),
          column: parseInt(warningMatch[3]),
          message: warningMatch[4],
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Check if a file exists in the workspace
   */
  private async fileExists(fileName: string): Promise<boolean> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return false;
      }

      const fileUri = vscode.Uri.joinPath(workspaceFolders[0].uri, fileName);
      await vscode.workspace.fs.stat(fileUri);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Find common source directories from Java file paths
   */
  private findSourceDirectories(javaFiles: string[]): string[] {
    const directories = new Set<string>();

    for (const file of javaFiles) {
      const dir = file.substring(0, file.lastIndexOf("/"));
      if (dir) {
        directories.add(dir);
      }
    }

    // Look for common patterns like src, src/main/java, etc.
    const commonSrcDirs = Array.from(directories).filter(
      (dir) => dir.includes("src") || dir.includes("java") || dir === "."
    );

    // If we found source-like directories, use them
    if (commonSrcDirs.length > 0) {
      return commonSrcDirs.slice(0, 3); // Limit to first 3 directories
    }

    // Otherwise, return unique directories (up to 3)
    return Array.from(directories).slice(0, 3);
  }

  /**
   * Clean file paths - DISABLED: Use file paths as-is to avoid path mismatches
   * Example: Keep paths exactly as provided by the server
   */
  private cleanFilePaths(filePaths: string[]): string[] {
    // Return paths as-is without any cleaning to avoid file path mismatches
    console.log(`📁 Using file paths as-is: ${filePaths.join(", ")}`);
    return filePaths;
  }
}
