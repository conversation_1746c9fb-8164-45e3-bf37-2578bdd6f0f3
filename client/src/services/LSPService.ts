import * as vscode from "vscode";

export interface LSPDiagnostic {
  file: string;
  line: number;
  column: number;
  severity: "error" | "warning" | "info" | "hint";
  message: string;
  source?: string;
  code?: string | number;
}

export interface LSPFileErrors {
  filePath: string;
  relativeFilePath: string;
  errors: LSPDiagnostic[];
  fileAccessError?: string; // Track file access errors
}

export class LSPService {
  private diagnosticCollection: vscode.DiagnosticCollection;

  constructor() {
    // Get the built-in diagnostic collection
    this.diagnosticCollection =
      vscode.languages.createDiagnosticCollection("lsp-feedback");
  }

  /**
   * Get diagnostics for specific files after code changes
   */
  public async getDiagnosticsForFiles(
    filePaths: string[]
  ): Promise<{ fileErrors: LSPFileErrors[]; hasFileAccessErrors: boolean }> {
    const fileErrors: LSPFileErrors[] = [];
    const workspaceRoot =
      vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || "";
    let hasFileAccessErrors = false;

    for (const filePath of filePaths) {
      try {
        // Skip empty or invalid paths
        if (
          !filePath ||
          typeof filePath !== "string" ||
          filePath.trim() === ""
        ) {
          console.warn(`⚠️ LSP skipping invalid file path: ${filePath}`);
          continue;
        }

        const trimmedPath = filePath.trim();

        // Convert to proper URI - handle both absolute and relative paths
        let fileUri: vscode.Uri;

        if (trimmedPath.startsWith("/") || trimmedPath.match(/^[A-Za-z]:/)) {
          // Absolute path - check if it's within workspace
          fileUri = vscode.Uri.file(trimmedPath);
          console.log(`🔍 LSP analyzing absolute path: ${trimmedPath}`);

          // Verify the absolute path is within the workspace
          if (!trimmedPath.startsWith(workspaceRoot)) {
            console.warn(`⚠️ Absolute path outside workspace: ${trimmedPath}`);
          }
        } else {
          // Relative path - normalize and clean it
          let cleanFilePath = trimmedPath;
          const workspaceUri = vscode.Uri.file(workspaceRoot);
          const workspaceFolderName = workspaceUri.path.split("/").pop();

          // Remove leading "./" if present
          if (cleanFilePath.startsWith("./")) {
            cleanFilePath = cleanFilePath.substring(2);
            console.log(
              `🔍 LSP removed ./ prefix: ${trimmedPath} → ${cleanFilePath}`
            );
          }

          // Remove workspace folder name prefix if present (but only once)
          if (
            workspaceFolderName &&
            cleanFilePath.startsWith(workspaceFolderName + "/")
          ) {
            cleanFilePath = cleanFilePath.substring(
              workspaceFolderName.length + 1
            );
            console.log(
              `🔍 LSP removed workspace folder prefix: ${trimmedPath} → ${cleanFilePath}`
            );
          }

          // Ensure clean path doesn't start with slash
          if (cleanFilePath.startsWith("/")) {
            cleanFilePath = cleanFilePath.substring(1);
            console.log(`🔍 LSP removed leading slash: ${cleanFilePath}`);
          }

          // Join with workspace root
          fileUri = vscode.Uri.joinPath(workspaceUri, cleanFilePath);
          console.log(
            `🔍 LSP analyzing relative path: ${trimmedPath} → ${fileUri.fsPath}`
          );
        }

        // Check if file exists before trying to open it
        try {
          await vscode.workspace.fs.stat(fileUri);
        } catch (statError) {
          console.error(`❌ File does not exist: ${fileUri.fsPath}`);
          hasFileAccessErrors = true;

          // Calculate relative path for error reporting
          const absolutePath = fileUri.fsPath;
          const relativeFilePath = absolutePath
            .replace(workspaceRoot, "")
            .replace(/^[\/\\]/, "");

          fileErrors.push({
            filePath: absolutePath,
            relativeFilePath: relativeFilePath,
            errors: [
              {
                file: absolutePath,
                line: 1,
                column: 1,
                severity: "error" as const,
                message: `File not found: ${filePath}`,
                source: "file-system",
              },
            ],
            fileAccessError: `File not found: ${filePath}`,
          });
          continue;
        }

        // Open the document to trigger LSP analysis
        const document = await vscode.workspace.openTextDocument(fileUri);

        // Wait a bit for LSP to analyze
        await this.waitForLSPAnalysis(document);

        // Get diagnostics from all diagnostic collections
        const diagnostics = this.getAllDiagnosticsForFile(fileUri);

        if (diagnostics.length > 0) {
          // Filter only errors (not warnings/info)
          const errors = diagnostics
            .filter((diag) => diag.severity === vscode.DiagnosticSeverity.Error)
            .map((diag) => this.convertDiagnostic(diag, filePath));

          if (errors.length > 0) {
            // Calculate relative path properly using absolute path
            const absolutePath = fileUri.fsPath;
            const relativeFilePath = absolutePath
              .replace(workspaceRoot, "")
              .replace(/^[\/\\]/, "");

            fileErrors.push({
              filePath: absolutePath, // Use absolute path
              relativeFilePath: relativeFilePath,
              errors: errors.map((error) => ({
                ...error,
                file: absolutePath, // Ensure error also has absolute path
              })),
            });
          }
        }
      } catch (error) {
        console.error(`❌ Error getting diagnostics for ${filePath}:`, error);
        hasFileAccessErrors = true;

        // Add file access error to results
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        fileErrors.push({
          filePath: filePath,
          relativeFilePath: filePath,
          errors: [
            {
              file: filePath,
              line: 1,
              column: 1,
              severity: "error" as const,
              message: `LSP analysis failed: ${errorMessage}`,
              source: "lsp-service",
            },
          ],
          fileAccessError: errorMessage,
        });
      }
    }

    return { fileErrors, hasFileAccessErrors };
  }

  /**
   * Get all diagnostics for a specific file from all diagnostic collections
   */
  private getAllDiagnosticsForFile(fileUri: vscode.Uri): vscode.Diagnostic[] {
    const allDiagnostics: vscode.Diagnostic[] = [];

    // Get diagnostics from all available diagnostic collections
    vscode.languages.getDiagnostics(fileUri).forEach((diagnostic) => {
      allDiagnostics.push(diagnostic);
    });

    return allDiagnostics;
  }

  /**
   * Wait for LSP to analyze the document
   */
  private async waitForLSPAnalysis(
    document: vscode.TextDocument
  ): Promise<void> {
    // Wait for LSP to process the document
    // This gives time for language servers to analyze the code
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Trigger validation by making a small edit and undoing it
    try {
      const edit = new vscode.WorkspaceEdit();
      const lastLine = document.lineCount - 1;
      const lastChar = document.lineAt(lastLine).text.length;
      const position = new vscode.Position(lastLine, lastChar);

      // Add a space and remove it to trigger validation
      edit.insert(document.uri, position, " ");
      await vscode.workspace.applyEdit(edit);

      // Wait a bit
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Undo the change
      const undoEdit = new vscode.WorkspaceEdit();
      undoEdit.delete(
        document.uri,
        new vscode.Range(position, new vscode.Position(lastLine, lastChar + 1))
      );
      await vscode.workspace.applyEdit(undoEdit);

      // Wait for final analysis
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      // If edit fails, just wait
      await new Promise((resolve) => setTimeout(resolve, 1500));
    }
  }

  /**
   * Convert VSCode diagnostic to our format
   */
  private convertDiagnostic(
    diagnostic: vscode.Diagnostic,
    filePath: string
  ): LSPDiagnostic {
    let severity: "error" | "warning" | "info" | "hint";

    switch (diagnostic.severity) {
      case vscode.DiagnosticSeverity.Error:
        severity = "error";
        break;
      case vscode.DiagnosticSeverity.Warning:
        severity = "warning";
        break;
      case vscode.DiagnosticSeverity.Information:
        severity = "info";
        break;
      case vscode.DiagnosticSeverity.Hint:
        severity = "hint";
        break;
      default:
        severity = "error";
    }

    return {
      file: filePath,
      line: diagnostic.range.start.line + 1, // Convert to 1-based
      column: diagnostic.range.start.character + 1, // Convert to 1-based
      severity: severity,
      message: diagnostic.message,
      source: diagnostic.source,
      code: diagnostic.code?.toString(),
    };
  }

  /**
   * Clear diagnostics
   */
  public clearDiagnostics(): void {
    this.diagnosticCollection.clear();
  }

  /**
   * Dispose of the service
   */
  public dispose(): void {
    this.diagnosticCollection.dispose();
  }
}
