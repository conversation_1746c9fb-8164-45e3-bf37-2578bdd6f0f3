/**
 * File Command Manager - Handles file operation commands from server using VSCode WorkspaceEdit API
 */
import * as vscode from "vscode";
import * as crypto from "crypto";

export interface FileCommand {
  command_id: string;
  type:
    | "replace_lines"
    | "insert_lines"
    | "delete_lines"
    | "create_file"
    | "delete_file";
  file_path: string;
  timestamp: number;
  start_line?: number;
  end_line?: number;
  line_number?: number;
  content?: string;
  file_version?: string;
}

export interface CommandAcknowledgment {
  command_id: string;
  status: "success" | "failure" | "conflict";
  message: string;
  timestamp: number;
  new_file_version?: string;
  error_details?: string;
  conflict_type?: string;
  expected_version?: string;
  actual_version?: string;
}

export class FileCommandManager {
  private decorationType: vscode.TextEditorDecorationType;
  private successDecorationType: vscode.TextEditorDecorationType;
  private errorDecorationType: vscode.TextEditorDecorationType;

  constructor() {
    // Create decoration types for visual feedback
    this.decorationType = vscode.window.createTextEditorDecorationType({
      backgroundColor: new vscode.ThemeColor(
        "editor.findMatchHighlightBackground"
      ),
      border: "1px solid",
      borderColor: new vscode.ThemeColor("editor.findMatchBorder"),
    });

    this.successDecorationType = vscode.window.createTextEditorDecorationType({
      backgroundColor: new vscode.ThemeColor(
        "diffEditor.insertedTextBackground"
      ),
      border: "1px solid",
      borderColor: new vscode.ThemeColor("diffEditor.insertedTextBorder"),
    });

    this.errorDecorationType = vscode.window.createTextEditorDecorationType({
      backgroundColor: new vscode.ThemeColor(
        "diffEditor.removedTextBackground"
      ),
      border: "1px solid",
      borderColor: new vscode.ThemeColor("diffEditor.removedTextBorder"),
    });
  }

  /**
   * Calculate file version using content hash
   */
  private calculateFileVersion(content: string): string {
    return crypto
      .createHash("sha256")
      .update(content, "utf8")
      .digest("hex")
      .substring(0, 16);
  }

  /**
   * Get current file content and version
   */
  private async getFileInfo(
    filePath: string
  ): Promise<{ content: string; version: string } | null> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return null;
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);

      try {
        const fileContent = await vscode.workspace.fs.readFile(fileUri);
        const content = new TextDecoder().decode(fileContent);
        const version = this.calculateFileVersion(content);
        return { content, version };
      } catch (error) {
        // File doesn't exist
        return null;
      }
    } catch (error) {
      console.error(`❌ Error getting file info for ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Execute a file command and return acknowledgment
   */
  async executeCommand(command: FileCommand): Promise<CommandAcknowledgment> {
    console.log(`🔧 Executing ${command.type} command: ${command.file_path}`);

    try {
      switch (command.type) {
        case "replace_lines":
          return await this.executeReplaceLines(command);
        case "insert_lines":
          return await this.executeInsertLines(command);
        case "delete_lines":
          return await this.executeDeleteLines(command);
        case "create_file":
          return await this.executeCreateFile(command);
        case "delete_file":
          return await this.executeDeleteFile(command);
        default:
          return {
            command_id: command.command_id,
            status: "failure",
            message: `Unknown command type: ${command.type}`,
            timestamp: Date.now(),
            error_details: `Unsupported command type: ${command.type}`,
          };
      }
    } catch (error) {
      console.error(`❌ Error executing command ${command.command_id}:`, error);
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Command execution failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Execute replace_lines command
   */
  private async executeReplaceLines(
    command: FileCommand
  ): Promise<CommandAcknowledgment> {
    if (
      !command.start_line ||
      !command.end_line ||
      command.content === undefined
    ) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: "Invalid replace_lines command: missing required parameters",
        timestamp: Date.now(),
      };
    }

    // Check file version if provided
    if (command.file_version) {
      const fileInfo = await this.getFileInfo(command.file_path);
      if (fileInfo && fileInfo.version !== command.file_version) {
        return {
          command_id: command.command_id,
          status: "conflict",
          message: "File version mismatch",
          timestamp: Date.now(),
          conflict_type: "version_mismatch",
          expected_version: command.file_version,
          actual_version: fileInfo.version,
        };
      }
    }

    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, command.file_path);

      // Create WorkspaceEdit
      const edit = new vscode.WorkspaceEdit();

      // CRITICAL FIX: Convert to 0-based indexing for VSCode API
      // This ensures we replace exactly the specified lines
      const startPos = new vscode.Position(command.start_line - 1, 0);
      const endPos = new vscode.Position(command.end_line, 0);
      const range = new vscode.Range(startPos, endPos);

      console.log(
        `🔧 FileCommandManager - Replacing lines ${command.start_line}-${command.end_line} in ${command.file_path}`
      );
      console.log(
        `🔧 FileCommandManager - Range: Line ${
          command.start_line
        } Col 0 to Line ${command.end_line + 1} Col 0`
      );
      console.log(
        `🔧 FileCommandManager - Content:`,
        JSON.stringify(command.content)
      );

      edit.replace(fileUri, range, command.content);

      // Apply the edit
      const success = await vscode.workspace.applyEdit(edit);

      if (success) {
        // Add visual feedback
        await this.addVisualFeedback(
          command.file_path,
          command.start_line - 1,
          command.end_line - 1,
          true
        );

        // Get new file version
        const newFileInfo = await this.getFileInfo(command.file_path);

        return {
          command_id: command.command_id,
          status: "success",
          message: `Successfully replaced lines ${command.start_line}-${command.end_line}`,
          timestamp: Date.now(),
          new_file_version: newFileInfo?.version,
        };
      } else {
        return {
          command_id: command.command_id,
          status: "failure",
          message: "Failed to apply workspace edit",
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Replace lines failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Execute insert_lines command
   */
  private async executeInsertLines(
    command: FileCommand
  ): Promise<CommandAcknowledgment> {
    if (!command.line_number || command.content === undefined) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: "Invalid insert_lines command: missing required parameters",
        timestamp: Date.now(),
      };
    }

    // Check file version if provided
    if (command.file_version) {
      const fileInfo = await this.getFileInfo(command.file_path);
      if (fileInfo && fileInfo.version !== command.file_version) {
        return {
          command_id: command.command_id,
          status: "conflict",
          message: "File version mismatch",
          timestamp: Date.now(),
          conflict_type: "version_mismatch",
          expected_version: command.file_version,
          actual_version: fileInfo.version,
        };
      }
    }

    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, command.file_path);

      // Create WorkspaceEdit
      const edit = new vscode.WorkspaceEdit();

      // Convert to 0-based indexing for VSCode API
      const position = new vscode.Position(command.line_number - 1, 0);

      edit.insert(fileUri, position, command.content + "\n");

      // Apply the edit
      const success = await vscode.workspace.applyEdit(edit);

      if (success) {
        // Add visual feedback
        await this.addVisualFeedback(
          command.file_path,
          command.line_number - 1,
          command.line_number - 1,
          true
        );

        // Get new file version
        const newFileInfo = await this.getFileInfo(command.file_path);

        return {
          command_id: command.command_id,
          status: "success",
          message: `Successfully inserted content at line ${command.line_number}`,
          timestamp: Date.now(),
          new_file_version: newFileInfo?.version,
        };
      } else {
        return {
          command_id: command.command_id,
          status: "failure",
          message: "Failed to apply workspace edit",
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Insert lines failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Execute delete_lines command
   */
  private async executeDeleteLines(
    command: FileCommand
  ): Promise<CommandAcknowledgment> {
    if (!command.start_line || !command.end_line) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: "Invalid delete_lines command: missing required parameters",
        timestamp: Date.now(),
      };
    }

    // Check file version if provided
    if (command.file_version) {
      const fileInfo = await this.getFileInfo(command.file_path);
      if (fileInfo && fileInfo.version !== command.file_version) {
        return {
          command_id: command.command_id,
          status: "conflict",
          message: "File version mismatch",
          timestamp: Date.now(),
          conflict_type: "version_mismatch",
          expected_version: command.file_version,
          actual_version: fileInfo.version,
        };
      }
    }

    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, command.file_path);

      // Create WorkspaceEdit
      const edit = new vscode.WorkspaceEdit();

      // Convert to 0-based indexing for VSCode API
      const startPos = new vscode.Position(command.start_line - 1, 0);
      const endPos = new vscode.Position(command.end_line, 0);
      const range = new vscode.Range(startPos, endPos);

      edit.delete(fileUri, range);

      // Apply the edit
      const success = await vscode.workspace.applyEdit(edit);

      if (success) {
        // Add visual feedback
        await this.addVisualFeedback(
          command.file_path,
          command.start_line - 1,
          command.end_line - 1,
          true
        );

        // Get new file version
        const newFileInfo = await this.getFileInfo(command.file_path);

        return {
          command_id: command.command_id,
          status: "success",
          message: `Successfully deleted lines ${command.start_line}-${command.end_line}`,
          timestamp: Date.now(),
          new_file_version: newFileInfo?.version,
        };
      } else {
        return {
          command_id: command.command_id,
          status: "failure",
          message: "Failed to apply workspace edit",
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Delete lines failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Execute create_file command
   */
  private async executeCreateFile(
    command: FileCommand
  ): Promise<CommandAcknowledgment> {
    if (command.content === undefined) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: "Invalid create_file command: missing content",
        timestamp: Date.now(),
      };
    }

    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, command.file_path);

      // Check if file already exists
      try {
        await vscode.workspace.fs.stat(fileUri);
        return {
          command_id: command.command_id,
          status: "conflict",
          message: "File already exists",
          timestamp: Date.now(),
          conflict_type: "file_exists",
        };
      } catch {
        // File doesn't exist, which is what we want
      }

      // Create directory if it doesn't exist
      const dirUri = vscode.Uri.joinPath(fileUri, "..");
      await vscode.workspace.fs.createDirectory(dirUri);

      // Create the file
      const encoder = new TextEncoder();
      await vscode.workspace.fs.writeFile(
        fileUri,
        encoder.encode(command.content)
      );

      // Get new file version
      const newFileInfo = await this.getFileInfo(command.file_path);

      console.log(`✅ File created: ${command.file_path}`);

      return {
        command_id: command.command_id,
        status: "success",
        message: `Successfully created file: ${command.file_path}`,
        timestamp: Date.now(),
        new_file_version: newFileInfo?.version,
      };
    } catch (error) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Create file failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Execute delete_file command
   */
  private async executeDeleteFile(
    command: FileCommand
  ): Promise<CommandAcknowledgment> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, command.file_path);

      // Check if file exists
      try {
        await vscode.workspace.fs.stat(fileUri);
      } catch {
        return {
          command_id: command.command_id,
          status: "conflict",
          message: "File not found",
          timestamp: Date.now(),
          conflict_type: "file_not_found",
        };
      }

      // Delete the file
      await vscode.workspace.fs.delete(fileUri);

      console.log(`✅ File deleted: ${command.file_path}`);

      return {
        command_id: command.command_id,
        status: "success",
        message: `Successfully deleted file: ${command.file_path}`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        command_id: command.command_id,
        status: "failure",
        message: `Delete file failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        timestamp: Date.now(),
        error_details: error instanceof Error ? error.stack : String(error),
      };
    }
  }

  /**
   * Add visual feedback for applied changes
   */
  private async addVisualFeedback(
    filePath: string,
    startLine: number,
    endLine: number,
    success: boolean
  ): Promise<void> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return;
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);

      // Find the editor for this file
      const editor = vscode.window.visibleTextEditors.find(
        (e) => e.document.uri.toString() === fileUri.toString()
      );

      if (editor) {
        const decorationType = success
          ? this.successDecorationType
          : this.errorDecorationType;
        const range = new vscode.Range(
          new vscode.Position(startLine, 0),
          new vscode.Position(endLine, Number.MAX_SAFE_INTEGER)
        );

        editor.setDecorations(decorationType, [range]);

        // Clear decoration after 3 seconds
        setTimeout(() => {
          editor.setDecorations(decorationType, []);
        }, 3000);
      }
    } catch (error) {
      console.error("❌ Error adding visual feedback:", error);
    }
  }

  /**
   * Get file version for conflict detection
   */
  async getFileVersion(filePath: string): Promise<string | null> {
    const fileInfo = await this.getFileInfo(filePath);
    return fileInfo?.version || null;
  }

  /**
   * Dispose of decoration types
   */
  dispose(): void {
    this.decorationType.dispose();
    this.successDecorationType.dispose();
    this.errorDecorationType.dispose();
  }
}
