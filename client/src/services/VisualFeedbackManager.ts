/**
 * Visual Feedback Manager - Provides visual feedback for file operations
 */
import * as vscode from "vscode";

export interface FeedbackOptions {
  type: "success" | "error" | "warning" | "info";
  duration?: number; // milliseconds
  showNotification?: boolean;
  showStatusBar?: boolean;
}

export class VisualFeedbackManager {
  private decorationTypes: Map<string, vscode.TextEditorDecorationType>;
  private statusBarItem: vscode.StatusBarItem;
  private activeDecorations: Map<string, vscode.Range[]>;

  constructor() {
    this.decorationTypes = new Map();
    this.activeDecorations = new Map();

    // Create status bar item
    this.statusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      100
    );

    this.initializeDecorationTypes();
  }

  private initializeDecorationTypes() {
    // Success decoration (green background)
    this.decorationTypes.set(
      "success",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor(
          "diffEditor.insertedTextBackground"
        ),
        border: "1px solid",
        borderColor: new vscode.ThemeColor("diffEditor.insertedTextBorder"),
        isWholeLine: true,
        overviewRulerColor: new vscode.ThemeColor(
          "diffEditor.insertedTextBorder"
        ),
        overviewRulerLane: vscode.OverviewRulerLane.Right,
        after: {
          contentText: " ✅",
          color: new vscode.ThemeColor("diffEditor.insertedTextBorder"),
        },
      })
    );

    // Error decoration (red background)
    this.decorationTypes.set(
      "error",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor(
          "diffEditor.removedTextBackground"
        ),
        border: "1px solid",
        borderColor: new vscode.ThemeColor("diffEditor.removedTextBorder"),
        isWholeLine: true,
        overviewRulerColor: new vscode.ThemeColor(
          "diffEditor.removedTextBorder"
        ),
        overviewRulerLane: vscode.OverviewRulerLane.Right,
        after: {
          contentText: " ❌",
          color: new vscode.ThemeColor("diffEditor.removedTextBorder"),
        },
      })
    );

    // Warning decoration (yellow background)
    this.decorationTypes.set(
      "warning",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor("editorWarning.background"),
        border: "1px solid",
        borderColor: new vscode.ThemeColor("editorWarning.border"),
        isWholeLine: true,
        overviewRulerColor: new vscode.ThemeColor("editorWarning.border"),
        overviewRulerLane: vscode.OverviewRulerLane.Right,
        after: {
          contentText: " ⚠️",
          color: new vscode.ThemeColor("editorWarning.foreground"),
        },
      })
    );

    // Info decoration (blue background)
    this.decorationTypes.set(
      "info",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor("editorInfo.background"),
        border: "1px solid",
        borderColor: new vscode.ThemeColor("editorInfo.border"),
        isWholeLine: true,
        overviewRulerColor: new vscode.ThemeColor("editorInfo.border"),
        overviewRulerLane: vscode.OverviewRulerLane.Right,
        after: {
          contentText: " ℹ️",
          color: new vscode.ThemeColor("editorInfo.foreground"),
        },
      })
    );

    // Modified lines decoration (subtle highlight)
    this.decorationTypes.set(
      "modified",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor(
          "editor.findMatchHighlightBackground"
        ),
        border: "1px solid",
        borderColor: new vscode.ThemeColor("editor.findMatchBorder"),
        isWholeLine: true,
        overviewRulerColor: new vscode.ThemeColor("editor.findMatchBorder"),
        overviewRulerLane: vscode.OverviewRulerLane.Left,
      })
    );

    // Processing decoration (animated)
    this.decorationTypes.set(
      "processing",
      vscode.window.createTextEditorDecorationType({
        backgroundColor: new vscode.ThemeColor("editor.selectionBackground"),
        border: "1px dashed",
        borderColor: new vscode.ThemeColor("editor.selectionForeground"),
        isWholeLine: true,
        after: {
          contentText: " 🔄",
          color: new vscode.ThemeColor("editor.selectionForeground"),
        },
      })
    );
  }

  /**
   * Show visual feedback for a file operation
   */
  async showFileFeedback(
    filePath: string,
    startLine: number,
    endLine: number,
    options: FeedbackOptions
  ): Promise<void> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return;
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);

      // Find or open the editor for this file
      let editor = vscode.window.visibleTextEditors.find(
        (e) => e.document.uri.toString() === fileUri.toString()
      );

      if (!editor) {
        // Try to open the file
        try {
          const document = await vscode.workspace.openTextDocument(fileUri);
          editor = await vscode.window.showTextDocument(document, {
            preview: false,
            preserveFocus: true,
          });
        } catch (error) {
          console.warn(`Could not open file for feedback: ${filePath}`);
          return;
        }
      }

      if (editor) {
        await this.applyDecoration(editor, startLine, endLine, options);
      }

      // Show notifications if requested
      if (options.showNotification) {
        this.showNotification(
          options.type,
          `File operation ${options.type} on ${filePath}`
        );
      }

      // Update status bar if requested
      if (options.showStatusBar) {
        this.updateStatusBar(
          options.type,
          `${options.type.toUpperCase()}: ${filePath}`
        );
      }
    } catch (error) {
      console.error("❌ Error showing file feedback:", error);
    }
  }

  /**
   * Apply decoration to editor
   */
  private async applyDecoration(
    editor: vscode.TextEditor,
    startLine: number,
    endLine: number,
    options: FeedbackOptions
  ): Promise<void> {
    const decorationType = this.decorationTypes.get(options.type);
    if (!decorationType) {
      return;
    }

    // Create range (convert to 0-based indexing)
    const range = new vscode.Range(
      new vscode.Position(Math.max(0, startLine - 1), 0),
      new vscode.Position(Math.max(0, endLine - 1), Number.MAX_SAFE_INTEGER)
    );

    // Apply decoration
    editor.setDecorations(decorationType, [range]);

    // Store active decoration for cleanup
    const fileKey = editor.document.uri.toString();
    if (!this.activeDecorations.has(fileKey)) {
      this.activeDecorations.set(fileKey, []);
    }
    this.activeDecorations.get(fileKey)!.push(range);

    // Auto-clear decoration after duration
    const duration = options.duration || 3000;
    setTimeout(() => {
      this.clearDecoration(editor, decorationType, range);
    }, duration);
  }

  /**
   * Clear specific decoration
   */
  private clearDecoration(
    editor: vscode.TextEditor,
    decorationType: vscode.TextEditorDecorationType,
    range: vscode.Range
  ): void {
    try {
      // Remove the specific decoration
      editor.setDecorations(decorationType, []);

      // Clean up from active decorations
      const fileKey = editor.document.uri.toString();
      const ranges = this.activeDecorations.get(fileKey);
      if (ranges) {
        const index = ranges.findIndex((r) => r.isEqual(range));
        if (index >= 0) {
          ranges.splice(index, 1);
        }
        if (ranges.length === 0) {
          this.activeDecorations.delete(fileKey);
        }
      }
    } catch (error) {
      console.error("❌ Error clearing decoration:", error);
    }
  }

  /**
   * Show notification message
   */
  private showNotification(type: string, message: string): void {
    switch (type) {
      case "success":
        vscode.window.showInformationMessage(`✅ ${message}`);
        break;
      case "error":
        vscode.window.showErrorMessage(`❌ ${message}`);
        break;
      case "warning":
        vscode.window.showWarningMessage(`⚠️ ${message}`);
        break;
      case "info":
        vscode.window.showInformationMessage(`ℹ️ ${message}`);
        break;
    }
  }

  /**
   * Update status bar
   */
  private updateStatusBar(type: string, message: string): void {
    const icons = {
      success: "✅",
      error: "❌",
      warning: "⚠️",
      info: "ℹ️",
    };

    this.statusBarItem.text = `${icons[type as keyof typeof icons]} ${message}`;
    this.statusBarItem.show();

    // Auto-hide after 5 seconds
    setTimeout(() => {
      this.statusBarItem.hide();
    }, 5000);
  }

  /**
   * Show processing indicator
   */
  async showProcessing(
    filePath: string,
    message: string = "Processing..."
  ): Promise<void> {
    await this.showFileFeedback(filePath, 1, 1, {
      type: "info",
      duration: 10000, // Longer duration for processing
      showStatusBar: true,
    });

    this.updateStatusBar("info", message);
  }

  /**
   * Clear all decorations for a file
   */
  clearFileDecorations(filePath: string): void {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return;
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);
      const fileKey = fileUri.toString();

      const editor = vscode.window.visibleTextEditors.find(
        (e) => e.document.uri.toString() === fileKey
      );

      if (editor) {
        // Clear all decoration types
        for (const decorationType of this.decorationTypes.values()) {
          editor.setDecorations(decorationType, []);
        }
      }

      // Clean up active decorations
      this.activeDecorations.delete(fileKey);
    } catch (error) {
      console.error("❌ Error clearing file decorations:", error);
    }
  }

  /**
   * Clear all decorations
   */
  clearAllDecorations(): void {
    for (const editor of vscode.window.visibleTextEditors) {
      for (const decorationType of this.decorationTypes.values()) {
        editor.setDecorations(decorationType, []);
      }
    }
    this.activeDecorations.clear();
    this.statusBarItem.hide();
  }

  /**
   * Dispose of all resources
   */
  dispose(): void {
    // Dispose decoration types
    for (const decorationType of this.decorationTypes.values()) {
      decorationType.dispose();
    }
    this.decorationTypes.clear();

    // Dispose status bar item
    this.statusBarItem.dispose();

    // Clear active decorations
    this.activeDecorations.clear();
  }
}
