import * as vscode from "vscode";
import * as crypto from "crypto";

/**
 * Smart Workspace Indexing Manager
 *
 * Handles persistent tracking of which workspaces have been indexed.
 * Ensures Index Codebase screen shows only once per unique workspace folder.
 */
export class WorkspaceIndexingManager {
  private _context: vscode.ExtensionContext;
  private static readonly INDEXED_WORKSPACES_KEY = "recode-indexed-workspaces";
  private static readonly CURRENT_WORKSPACE_KEY = "recode-current-workspace";
  private static readonly WORKSPACE_STATE_KEY = "recode-workspace-state";

  // In-memory cache to handle VSCode state issues
  private _indexedWorkspacesCache: string[] = [];
  private _cacheLoaded: boolean = false;

  constructor(context: vscode.ExtensionContext) {
    this._context = context;
    // Load cache immediately
    this.loadCache();
  }

  /**
   * Generate a unique, stable identifier for the current workspace
   */
  private generateWorkspaceFingerprint(): string | null {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      return null;
    }

    // Use the first workspace folder's absolute path as the primary identifier
    const workspacePath = workspaceFolders[0].uri.fsPath;

    // Create a stable hash of the workspace path
    const hash = crypto.createHash("sha256");
    hash.update(workspacePath);
    const fingerprint = hash.digest("hex").substring(0, 16);

    console.log(
      `🔍 Generated workspace fingerprint: ${fingerprint} for path: ${workspacePath}`
    );
    return fingerprint;
  }

  /**
   * Load indexed workspaces cache from multiple storage sources
   */
  private async loadCache(): Promise<void> {
    try {
      console.log("🔄 Loading workspace indexing cache...");

      // Try multiple storage sources
      let indexedWorkspaces: string[] = [];

      // Source 1: Global state
      const globalState = this._context.globalState.get<string[]>(
        WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
        []
      );

      // Source 2: Workspace state
      const workspaceState = this._context.workspaceState.get<string[]>(
        WorkspaceIndexingManager.WORKSPACE_STATE_KEY,
        []
      );

      // Merge and deduplicate
      indexedWorkspaces = [...new Set([...globalState, ...workspaceState])];

      this._indexedWorkspacesCache = indexedWorkspaces;
      this._cacheLoaded = true;

      console.log(`✅ Cache loaded: [${indexedWorkspaces.join(", ")}]`);
      console.log(
        `📊 Sources - Global: ${globalState.length}, Workspace: ${workspaceState.length}, Merged: ${indexedWorkspaces.length}`
      );
    } catch (error) {
      console.error("❌ Failed to load workspace cache:", error);
      this._indexedWorkspacesCache = [];
      this._cacheLoaded = true;
    }
  }

  /**
   * Save indexed workspaces to multiple storage sources
   */
  private async saveCache(): Promise<void> {
    try {
      console.log(
        `💾 Saving workspace cache: [${this._indexedWorkspacesCache.join(
          ", "
        )}]`
      );

      // Save to both global and workspace state for redundancy
      await Promise.all([
        this._context.globalState.update(
          WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
          this._indexedWorkspacesCache
        ),
        this._context.workspaceState.update(
          WorkspaceIndexingManager.WORKSPACE_STATE_KEY,
          this._indexedWorkspacesCache
        ),
      ]);

      // Verify saves worked
      const globalVerify = this._context.globalState.get<string[]>(
        WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
        []
      );
      const workspaceVerify = this._context.workspaceState.get<string[]>(
        WorkspaceIndexingManager.WORKSPACE_STATE_KEY,
        []
      );

      console.log(
        `✅ Save verification - Global: ${globalVerify.length}, Workspace: ${workspaceVerify.length}`
      );
    } catch (error) {
      console.error("❌ Failed to save workspace cache:", error);
    }
  }

  /**
   * Check if the current workspace has been indexed before
   */
  public isWorkspaceIndexed(): boolean {
    const fingerprint = this.generateWorkspaceFingerprint();
    if (!fingerprint) {
      console.log("🔍 No workspace folder found - not indexed");
      return false;
    }

    // Ensure cache is loaded
    if (!this._cacheLoaded) {
      console.log("⚠️ Cache not loaded yet, loading synchronously...");
      // Force synchronous load for immediate check
      this.loadCache();
    }

    // Use cache for faster, more reliable access
    const isIndexed = this._indexedWorkspacesCache.includes(fingerprint);

    // Also check VSCode state as backup
    const globalState: string[] = this._context.globalState.get(
      WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
      []
    );
    const workspaceState: string[] = this._context.workspaceState.get(
      WorkspaceIndexingManager.WORKSPACE_STATE_KEY,
      []
    );

    const isIndexedGlobal = globalState.includes(fingerprint);
    const isIndexedWorkspace = workspaceState.includes(fingerprint);

    // Enhanced debug logging
    console.log(`🔍 === WORKSPACE INDEXING DETAILED DEBUG ===`);
    console.log(`🔍 Current workspace fingerprint: ${fingerprint}`);
    console.log(`🔍 Cache loaded: ${this._cacheLoaded}`);
    console.log(
      `🔍 Cache contents: [${this._indexedWorkspacesCache.join(", ")}]`
    );
    console.log(`🔍 Global state: [${globalState.join(", ")}]`);
    console.log(`🔍 Workspace state: [${workspaceState.join(", ")}]`);
    console.log(`🔍 Is indexed (cache): ${isIndexed}`);
    console.log(`🔍 Is indexed (global): ${isIndexedGlobal}`);
    console.log(`🔍 Is indexed (workspace): ${isIndexedWorkspace}`);

    // Additional debugging
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
      console.log(`🔍 Workspace path: ${workspaceFolders[0].uri.fsPath}`);
    }

    // Check if fingerprint generation is consistent
    const fingerprint2 = this.generateWorkspaceFingerprint();
    console.log(
      `🔍 Fingerprint consistency: ${
        fingerprint === fingerprint2 ? "CONSISTENT" : "INCONSISTENT"
      }`
    );

    // Final decision logic
    const finalDecision = isIndexed || isIndexedGlobal || isIndexedWorkspace;
    console.log(`🔍 FINAL INDEXED STATUS: ${finalDecision}`);
    console.log(`🔍 === END DETAILED DEBUG ===`);

    return finalDecision;
  }

  /**
   * Mark the current workspace as indexed
   */
  public async markWorkspaceAsIndexed(): Promise<void> {
    const fingerprint = this.generateWorkspaceFingerprint();
    if (!fingerprint) {
      console.log("⚠️ Cannot mark workspace as indexed - no workspace folder");
      return;
    }

    console.log(`🔍 === MARKING WORKSPACE AS INDEXED ===`);
    console.log(`🔍 Workspace fingerprint: ${fingerprint}`);

    // Ensure cache is loaded
    if (!this._cacheLoaded) {
      await this.loadCache();
    }

    console.log(
      `🔍 Current cache before: [${this._indexedWorkspacesCache.join(", ")}]`
    );

    // Add current workspace if not already present
    if (!this._indexedWorkspacesCache.includes(fingerprint)) {
      this._indexedWorkspacesCache.push(fingerprint);
      console.log(`✅ Added ${fingerprint} to cache`);
    } else {
      console.log(`ℹ️ Workspace ${fingerprint} already in cache`);
    }

    // Save to persistent storage
    await this.saveCache();

    // Also store current workspace for reference
    await this._context.globalState.update(
      WorkspaceIndexingManager.CURRENT_WORKSPACE_KEY,
      fingerprint
    );

    console.log(`📝 Final cache: [${this._indexedWorkspacesCache.join(", ")}]`);
    console.log(`🔍 === END MARKING ===`);
  }

  /**
   * Reset indexing status for the current workspace (for testing/debugging)
   */
  public async resetCurrentWorkspaceIndexing(): Promise<void> {
    const fingerprint = this.generateWorkspaceFingerprint();
    if (!fingerprint) {
      console.log("⚠️ Cannot reset workspace indexing - no workspace folder");
      return;
    }

    // Ensure cache is loaded
    if (!this._cacheLoaded) {
      await this.loadCache();
    }

    // Remove current workspace from cache
    this._indexedWorkspacesCache = this._indexedWorkspacesCache.filter(
      (ws) => ws !== fingerprint
    );

    // Save updated cache
    await this.saveCache();

    console.log(`🔄 Reset indexing status for workspace ${fingerprint}`);
    console.log(
      `📝 Updated cache: [${this._indexedWorkspacesCache.join(", ")}]`
    );
  }

  /**
   * Get debug information about workspace indexing state
   */
  public getDebugInfo(): any {
    const fingerprint = this.generateWorkspaceFingerprint();
    const indexedWorkspaces: string[] = this._context.globalState.get(
      WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
      []
    );
    const currentWorkspace = this._context.globalState.get(
      WorkspaceIndexingManager.CURRENT_WORKSPACE_KEY
    );

    return {
      currentWorkspaceFingerprint: fingerprint,
      isCurrentWorkspaceIndexed: fingerprint
        ? indexedWorkspaces.includes(fingerprint)
        : false,
      allIndexedWorkspaces: indexedWorkspaces,
      storedCurrentWorkspace: currentWorkspace,
      workspaceFolders: vscode.workspace.workspaceFolders?.map((f) => ({
        name: f.name,
        path: f.uri.fsPath,
      })),
    };
  }

  /**
   * Determine which screen to show based on workspace indexing status
   */
  public shouldShowIndexingScreen(): boolean {
    const workspaceFolders = vscode.workspace.workspaceFolders;

    // No workspace folder = show indexing screen with error
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.log("🔍 No workspace folder - should show indexing screen");
      return true;
    }

    // Check if this workspace has been indexed before
    const isIndexed = this.isWorkspaceIndexed();

    if (isIndexed) {
      console.log("🔍 Workspace already indexed - should show chat screen");
      return false;
    } else {
      console.log("🔍 New workspace - should show indexing screen");
      return true;
    }
  }

  /**
   * Clean up old workspace entries (optional maintenance)
   */
  public async cleanupOldWorkspaces(maxEntries: number = 50): Promise<void> {
    const indexedWorkspaces: string[] = this._context.globalState.get(
      WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
      []
    );

    if (indexedWorkspaces.length > maxEntries) {
      // Keep only the most recent entries (simple FIFO cleanup)
      const trimmedWorkspaces = indexedWorkspaces.slice(-maxEntries);

      await this._context.globalState.update(
        WorkspaceIndexingManager.INDEXED_WORKSPACES_KEY,
        trimmedWorkspaces
      );

      console.log(
        `🧹 Cleaned up old workspace entries: ${indexedWorkspaces.length} -> ${trimmedWorkspaces.length}`
      );
    }
  }
}
