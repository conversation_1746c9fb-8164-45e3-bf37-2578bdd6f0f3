import * as vscode from "vscode";
import WebSocket from "ws";

export interface WebSocketMessage {
  type: string;
  data?: any;
}

export interface WebSocketMessageHandler {
  (message: WebSocketMessage): Promise<void>;
}

export class WebSocketManager {
  private _websocket?: WebSocket;
  private _clientId: string;
  private _messageHandler?: WebSocketMessageHandler;
  private _reconnectAttempts: number = 0;
  private _maxReconnectAttempts: number = 5;
  private _reconnectDelay: number = 1000; // Start with 1 second
  private _isReconnecting: boolean = false;
  private _shouldReconnect: boolean = true;

  constructor(clientId: string) {
    this._clientId = clientId;
  }

  public setMessageHandler(handler: WebSocketMessageHandler) {
    this._messageHandler = handler;
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const host = process.env.RECODE_SERVER_HOST || "127.0.0.1";
        const port = process.env.RECODE_SERVER_PORT || "8000";
        const wsUrl = `ws://${host}:${port}/ws/coder/${this._clientId}`;

        this._websocket = new WebSocket(wsUrl);

        this._websocket.on("open", () => {
          console.log("🔌 WebSocket connected for coder agent");
          resolve();
        });

        this._websocket.on("message", (data: WebSocket.Data) => {
          const message = data.toString();
          this.handleMessage(message);
        });

        this._websocket.on("close", () => {
          console.log("🔌 WebSocket disconnected");
          this._websocket = undefined;
        });

        this._websocket.on("error", (error) => {
          console.error("❌ WebSocket error:", error);
          reject(error);
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          if (this._websocket?.readyState !== WebSocket.OPEN) {
            reject(new Error("WebSocket connection timeout"));
          }
        }, 10000);
      } catch (error) {
        reject(error);
      }
    });
  }

  public send(message: WebSocketMessage) {
    if (this._websocket && this._websocket.readyState === WebSocket.OPEN) {
      this._websocket.send(JSON.stringify(message));
    } else {
      console.error("❌ WebSocket not connected");
    }
  }

  public isConnected(): boolean {
    return this._websocket?.readyState === WebSocket.OPEN;
  }

  public disconnect() {
    if (this._websocket) {
      this._websocket.close();
      this._websocket = undefined;
    }
  }

  public ping() {
    if (this._websocket && this._websocket.readyState === WebSocket.OPEN) {
      try {
        this._websocket.ping();
        console.log("🔄 WebSocket ping sent to maintain connection");
      } catch (error) {
        console.log("🔄 WebSocket ping failed:", error);
      }
    }
  }

  private async handleMessage(data: string) {
    try {
      const message = JSON.parse(data);
      if (this._messageHandler) {
        await this._messageHandler(message);
      }
    } catch (error) {
      console.error("❌ Error handling WebSocket message:", error);
    }
  }
}
