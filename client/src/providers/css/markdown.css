/* Markdown content styles */
.markdown-content {
    white-space: normal !important;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
    color: var(--vscode-foreground);
    margin: 16px 0 8px 0;
    font-weight: 600;
}

.markdown-content h1 {
    font-size: 18px;
    border-bottom: 1px solid var(--vscode-panel-border);
    padding-bottom: 4px;
}

.markdown-content h2 {
    font-size: 16px;
}

.markdown-content h3 {
    font-size: 14px;
}

.markdown-content strong {
    font-weight: 600;
    color: var(--vscode-foreground);
}

.markdown-content em {
    font-style: italic;
    color: var(--vscode-foreground);
}

.markdown-content code {
    background-color: var(--vscode-textCodeBlock-background);
    color: var(--vscode-textPreformat-foreground);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
}

.markdown-content pre {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    overflow-x: auto;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: var(--vscode-textPreformat-foreground);
    font-size: 12px;
    line-height: 1.4;
}

.markdown-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.markdown-content li {
    margin: 4px 0;
    color: var(--vscode-foreground);
}

.markdown-content br {
    line-height: 1.6;
}
