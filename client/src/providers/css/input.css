/* Chat input and interaction styles */
.chat-input-area {
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 12px;
  padding: 12px;
  position: sticky; /* Back to sticky */
  bottom: 8px; /* Small gap from bottom */
  margin: 8px 15px 8px 15px; /* Restore margins */
  z-index: 100;
  flex-shrink: 0; /* Prevent shrinking */
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); /* Original shadow */
}

.chat-input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 20px;
  position: relative;
}

.chat-input {
  flex: 1;
  min-height: 60px;
  max-height: 150px;
  border: none;
  background: transparent;
  color: var(--vscode-input-foreground);
  font-size: 14px;
  font-family: var(--vscode-font-family);
  resize: none;
  outline: none;
  line-height: 1.5;
  overflow-y: auto;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.chat-input:focus {
  border-color: var(--vscode-focusBorder);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.chat-input::placeholder {
  color: var(--vscode-input-placeholderForeground);
  font-style: italic;
}

.chat-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-btn {
  background: none;
  border: none;
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.send-btn {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stop-btn {
  background-color: #ff4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.stop-btn:hover {
  background-color: #cc3333;
}

.editor-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attachment-btn {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: 4px;
  padding: 6px;
  font-size: 14px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-btn:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.btn-send {
  background-color: #007acc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px;
  font-size: 14px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-send:hover {
  background-color: #005a9e;
}

.btn-send:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.attachment-list {
  margin-top: 8px;
  font-size: 11px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 0;
  color: var(--vscode-descriptionForeground);
}

.attachment-item .remove-btn {
  cursor: pointer;
  opacity: 0.7;
}

.attachment-item .remove-btn:hover {
  opacity: 1;
}

.rich-editor-container {
  margin-top: 20px;
}

.ask-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
}

.ask-toggle input[type="checkbox"] {
  margin-right: 8px;
}

.rich-editor {
  min-height: 100px;
  border: 1px solid var(--vscode-input-border);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-radius: 3px;
  padding: 8px;
  font-size: 12px;
  resize: vertical;
  width: 100%;
  box-sizing: border-box;
}

.attachment-area {
  margin-top: 8px;
  padding: 8px;
  border: 1px dashed var(--vscode-panel-border);
  border-radius: 3px;
  text-align: center;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
}

.attachment-area:hover {
  background-color: var(--vscode-list-hoverBackground);
}
