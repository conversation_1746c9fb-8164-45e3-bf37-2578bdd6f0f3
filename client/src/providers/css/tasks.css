/* Task list and collapsible UI styles */
.task-list-message {
  margin: 12px 0;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.task-list-header {
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  background: var(--vscode-sideBar-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.task-list-header:hover {
  background: var(--vscode-list-hoverBackground);
}

.task-list-header .status-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-list-header .status-icon {
  font-size: 14px;
  color: var(--vscode-foreground);
}

.task-list-header .status-text {
  font-weight: 600;
  color: #ffffff;
  font-size: 12px;
  flex: 1;
  text-align: left;
}

.task-list-content {
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 400px;
  overflow: hidden;
  background: #2d3748;
}

.task-list-content.collapsed {
  max-height: 0;
  padding: 0;
}

.task-item {
  display: flex;
  align-items: flex-start;
  margin: 0;
  padding: 12px 16px;
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: background-color 0.2s ease;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.task-item:last-child {
  border-bottom: none;
}

.task-number {
  color: #81c784;
  font-weight: 700;
  margin-right: 12px;
  flex-shrink: 0;
  min-width: 24px;
  font-size: 11px;
  background: rgba(129, 199, 132, 0.15);
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
}

.task-text {
  color: #e2e8f0;
  line-height: 1.5;
  flex: 1;
  font-size: 11px;
  text-align: left;
}

.collapse-icon {
  margin-left: auto;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--vscode-foreground);
  font-size: 12px;
  font-weight: bold;
}

.task-list-message.collapsed .collapse-icon {
  transform: rotate(90deg);
}

/* Tasks Identified Styles */
.tasks-identified {
  margin: 12px 0;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.tasks-header-collapsible {
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  background: var(--vscode-sideBar-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.tasks-header-collapsible:hover {
  background: var(--vscode-list-hoverBackground);
}

.tasks-header-collapsible,
.task-list-header {
  background: #232323;
  transition: background 0.2s;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.tasks-header-collapsible:hover,
.task-list-header:hover {
  background: #2d2d2d;
  cursor: pointer;
}

/* DEPRECATED: Use .action-status instead */
.action-highlight {
  background: #232323;
  border-top: 1px solid #444;
  border-bottom: 1px solid #444;
  padding: 8px 12px;
  margin: 8px 0;
  color: #f2f2f2;
  font-family: inherit;
  font-size: 1em;
  font-weight: 500;
}

.tasks-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tasks-title-row h4 {
  margin: 0;
  color: var(--vscode-foreground);
  font-size: 12px;
  font-weight: 500;
}

.tasks-count {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.collapse-indicator {
  margin-left: auto;
  transition: transform 0.3s ease;
  color: var(--vscode-foreground);
  font-size: 10px;
}

.tasks-list.collapsed .collapse-indicator {
  transform: rotate(-90deg);
}

.tasks-list {
  max-height: 400px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.tasks-list.collapsed {
  max-height: 0;
  overflow: hidden;
}

.task-item-clean {
  display: flex;
  align-items: flex-start;
  padding: 8px 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
  gap: 12px;
}

.task-item-clean:last-child {
  border-bottom: none;
}

.task-content {
  flex: 1;
}

.task-description-clean {
  color: var(--vscode-foreground);
  font-size: 11px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.task-target,
.task-action,
.task-details {
  color: var(--vscode-descriptionForeground);
  font-size: 10px;
  margin-bottom: 2px;
}

.task-target {
  font-family: var(--vscode-editor-font-family);
}

.no-tasks-message {
  padding: 16px;
  text-align: center;
  color: var(--vscode-descriptionForeground);
  font-size: 11px;
  font-style: italic;
}

/* Execution Plan Specific Styles - Professional Design */
.execution-plan-message {
  margin: 12px 0;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.execution-plan-message .task-list-header {
  background: var(--vscode-sideBar-background);
  border-bottom: 1px solid var(--vscode-panel-border);
  padding: 12px 16px;
}

.execution-plan-message .task-list-header:hover {
  background: var(--vscode-list-hoverBackground);
}

.execution-plan-message .status-icon {
  color: var(--vscode-foreground);
  font-size: 14px;
}

.execution-plan-message .status-text {
  color: var(--vscode-foreground);
  font-weight: 500;
  font-size: 12px;
  text-align: left;
}

.execution-plan-message .collapse-icon {
  color: var(--vscode-foreground);
  opacity: 0.7;
}

/* Professional Loading Dots Animation */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.loading-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--vscode-foreground);
  opacity: 0.4;
  animation: loading-dots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-dots {
  0%,
  80%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Unified Action Styles - Based on Tasks Identified Design */
.action-status {
  margin: 12px 0;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.action-status-header {
  padding: 12px 16px;
  background: #232323;
  border-bottom: 1px solid var(--vscode-panel-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.2s;
}

.action-status-header.with-dropdown {
  cursor: pointer;
  user-select: none;
}

.action-status-header.with-dropdown:hover {
  background: #2d2d2d;
}

.action-status-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--vscode-foreground);
  font-weight: 500;
  font-size: var(--vscode-font-size);
}

.action-status-spinner {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.action-status-badge {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.85em;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

.action-status-dropdown-icon {
  transition: transform 0.2s ease;
  color: var(--vscode-foreground);
  opacity: 0.7;
}

.action-status.collapsed .action-status-dropdown-icon {
  transform: rotate(90deg);
}

/* Action Status Container */
.action-status-container {
  margin: 0;
  padding: 0;
}

.action-status-container .action-status {
  margin: 8px 0;
}

/* Execution Step Items - Clean Professional Design */
.execution-step-item {
  display: block;
  padding: 0;
  border-bottom: 1px solid var(--vscode-panel-border);
  background: transparent;
}

.execution-step-item:last-child {
  border-bottom: none;
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: transparent;
}

.step-number {
  color: var(--vscode-foreground);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 13px;
  flex-shrink: 0;
  opacity: 0.7;
}

.step-number::before {
  content: "•";
  font-size: 16px;
}

.step-info {
  flex: 1;
  min-width: 0;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 4px;
}

.step-type-badge {
  color: var(--vscode-foreground);
  font-size: 11px;
  font-weight: 500;
  opacity: 0.8;
  text-align: left;
}

.action-type-badge {
  color: var(--vscode-descriptionForeground);
  font-size: 10px;
  font-weight: 400;
  opacity: 0.7;
  text-align: left;
}

.step-content {
  padding: 0 16px 16px 32px;
}

.execution-details {
  color: var(--vscode-foreground);
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 11px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  opacity: 0.9;
  text-align: left;
}

.step-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.metadata-item {
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--vscode-descriptionForeground);
  opacity: 0.8;
  text-align: left;
}

.metadata-item.target-folder {
  color: var(--vscode-descriptionForeground);
}

.metadata-item.prerequisites {
  color: var(--vscode-descriptionForeground);
}

.metadata-item.effort-estimate {
  color: var(--vscode-descriptionForeground);
  font-weight: 500;
}

.action-title-message {
  font-size: 1.15em;
  font-weight: 600;
  margin: 8px 0 4px 0;
  font-family: inherit;
}

.status-message,
.status-text,
.task-item-clean,
.task-description-clean {
  font-family: inherit;
  font-weight: normal;
}

.action-heading-block {
  display: flex;
  align-items: center;
  margin: 20px 0 12px 0;
  font-family: inherit;
}

.action-number-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #444;
  color: #fff;
  font-weight: 600;
  font-size: 0.8em;
  margin-right: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

.action-heading-text {
  font-size: 1em;
  font-weight: 600;
  color: #fff;
  letter-spacing: 0.01em;
}

.action-heading-h2 {
  display: block;
  font-size: calc(var(--vscode-font-size, 14px) + 1px) !important;
  font-weight: 600;
  line-height: 1.3;
  margin: 12px 0 8px 0;
  color: #fff;
  letter-spacing: 0.01em;
}

.action-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  border-top: 2px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
  vertical-align: middle;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Tool Status Indicator - consistent placement for spinner and completion icons */
.tool-status-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
}

.tool-status-indicator .action-status-icon {
  font-size: 14px;
}

.tool-status-indicator .action-status-icon.success {
  color: #28a745;
}

.tool-status-indicator .action-status-icon.error {
  color: #dc3545;
}

/* Action Status Controls */
.action-status-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-status-icon {
  font-size: 14px;
  min-width: 20px;
  text-align: center;
}

.action-status-icon.success {
  color: #28a745;
}

.action-status-icon.error {
  color: #dc3545;
}

.action-status-icon.in-progress {
  color: #ffc107;
}

.action-file-link {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 2px 4px;
  border-radius: 2px;
}

.action-file-link:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.action-file-path {
  padding: 0 16px 8px 16px;
  font-size: 0.85em;
  color: var(--vscode-foreground);
  opacity: 0.7;
  font-family: var(--vscode-editor-font-family);
  word-break: break-all;
  line-height: 1.4;
}
