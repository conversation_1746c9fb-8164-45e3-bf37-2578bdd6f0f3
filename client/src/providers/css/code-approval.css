/* Code Approval UI Styles */
#code-approval-section {
  display: none;
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  padding: 16px;
  margin: 10px 0;
  text-align: left;
}

.approval-header h3 {
  margin: 0 0 8px 0;
  color: var(--vscode-foreground);
  font-size: 16px;
}

.approval-summary {
  margin: 0 0 16px 0;
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
}

.approval-details {
  margin-bottom: 16px;
}

.reason-section,
.details-section,
.operations-section {
  margin-bottom: 12px;
}

.reason-section h4,
.details-section h4,
.operations-section h4 {
  margin: 0 0 6px 0;
  color: var(--vscode-foreground);
  font-size: 13px;
  font-weight: 600;
}

.reason-section p,
.details-section p {
  margin: 0;
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
  line-height: 1.4;
  background-color: var(--vscode-textCodeBlock-background);
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid var(--vscode-textLink-foreground);
}

.operations-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background-color: var(--vscode-textCodeBlock-background);
}

.operation-item {
  padding: 8px;
  border-bottom: 1px solid var(--vscode-panel-border);
  font-size: 11px;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.operation-number {
  color: var(--vscode-textLink-foreground);
  font-weight: 600;
  min-width: 20px;
}

.operation-file {
  color: var(--vscode-foreground);
  font-weight: 500;
  flex: 1;
}

.operation-action {
  color: var(--vscode-descriptionForeground);
  background-color: var(--vscode-badge-background);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  text-transform: uppercase;
}

.operation-details {
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  margin-left: 28px;
}

.approval-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.approve-btn,
.reject-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  min-width: 100px;
  transition: background-color 0.2s;
}

.approve-btn {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.approve-btn:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.reject-btn {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.reject-btn:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

/* Professional Operation Item Styling */
.operation-item {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 12px;
  margin: 8px 0;
  background: transparent;
}

.operation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.operation-title {
  color: var(--vscode-foreground);
  font-weight: 500;
  word-break: break-word;
  flex: 1;
  min-width: 0;
  font-size: 13px;
}

.operation-action {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  font-weight: 500;
}

.operation-description {
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
  margin-bottom: 8px;
  opacity: 0.8;
}

.terminal-commands {
  background: transparent;
  border: none;
  padding: 0;
  margin: 8px 0;
}

/* VSCode Terminal Style */
.terminal-command-item {
  background: #1e1e1e;
  border: 1px solid #3c3c3c;
  border-radius: 6px;
  margin: 4px 0;
  overflow: hidden;
}

.terminal-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #1e1e1e;
  gap: 8px;
}

.terminal-icon {
  color: #d4a574;
  font-size: 14px;
  flex-shrink: 0;
}

.terminal-command {
  color: #d4d4d4;
  font-family: "Menlo", "Monaco", "Courier New", monospace;
  font-size: 13px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.terminal-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.terminal-option {
  color: #9d9d9d;
  font-size: 12px;
  cursor: pointer;
}

.terminal-skip {
  color: #9d9d9d;
  font-size: 12px;
  cursor: pointer;
}

.terminal-run-btn {
  background: #0e639c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.terminal-run-btn:hover {
  background: #1177bb;
}

.code-approval-message {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  background: transparent;
}

.approval-status {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 13px;
  text-align: center;
}

.approval-status.approved {
  background: var(--vscode-textCodeBlock-background);
  color: var(--vscode-foreground);
  border: 1px solid var(--vscode-panel-border);
}

.approval-status.rejected {
  background: var(--vscode-textCodeBlock-background);
  color: var(--vscode-foreground);
  border: 1px solid var(--vscode-panel-border);
}

/* Clean Action-Status Style for Code Approval */
.action-play-button {
  background: transparent;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  color: var(--vscode-foreground);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.action-play-button:hover {
  background: var(--vscode-button-hoverBackground);
  color: var(--vscode-button-foreground);
  transform: scale(1.05);
}

.approval-actions-container {
  display: flex;
  gap: 12px;
  margin: 16px 0;
  justify-content: center;
}

.approve-changes-btn,
.reject-changes-btn {
  padding: 8px 16px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: transparent;
  color: var(--vscode-foreground);
}

.approve-changes-btn:hover {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-color: var(--vscode-button-background);
}

.reject-changes-btn:hover {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border-color: var(--vscode-button-secondaryBackground);
}

/* Code Operation Styling */
.code-operation {
  margin-bottom: 12px !important;
  border: 2px solid #22c55e !important; /* Green border for code changes */
  border-radius: 6px !important;
}

.terminal-command {
  margin-bottom: 12px !important;
  border: 2px solid #22c55e !important; /* Green border for terminal commands */
  border-radius: 6px !important;
  animation: pulse-border 2s infinite; /* Add pulsing animation */
  min-height: 50px; /* Ensure minimum height for play button visibility */
  overflow: hidden; /* Prevent overflow */
}

.terminal-command .action-status-header {
  align-items: flex-start !important; /* Align to top for wrapping */
  padding: 8px !important;
  display: flex !important;
  gap: 8px !important;
}

.terminal-command .action-status-content {
  flex: 1;
  min-width: 0; /* Allow shrinking */
  display: flex !important;
  align-items: flex-start !important;
  gap: 6px !important;
  overflow: hidden; /* Prevent content overflow */
}

.terminal-command .terminal-icon {
  font-size: 14px;
  flex-shrink: 0;
  margin-top: 1px;
}

.terminal-command .action-text {
  font-size: 11px !important;
  line-height: 1.3 !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
  color: #ec4899 !important; /* Pink color for terminal commands */
  font-family: var(--vscode-editor-font-family) !important;
  display: block !important;
  flex: 1 !important;
  overflow-wrap: break-word !important;
  max-height: 60px; /* Limit height */
  overflow-y: auto; /* Add scroll if needed */
  padding-right: 4px; /* Space for scrollbar */
}

.terminal-command .action-play-button {
  flex-shrink: 0 !important;
  margin-top: 2px !important;
  width: 32px !important; /* Fixed width */
  height: 32px !important; /* Fixed height */
  min-width: 32px !important; /* Ensure minimum width */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

@keyframes pulse-border {
  0% {
    border-color: #22c55e;
  }
  50% {
    border-color: #16a34a;
  }
  100% {
    border-color: #22c55e;
  }
}

/* Responsive handling for very long commands */
@media (max-width: 400px) {
  .terminal-command .action-text {
    font-size: 10px !important;
    max-height: 40px !important;
  }

  .terminal-command .action-play-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
  }
}

/* Ensure play button is always visible even with very long text */
.terminal-command .action-status-header {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.operation-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-open-link {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
  font-size: 11px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s ease;
  border: 1px solid var(--vscode-panel-border);
}

.file-open-link:hover {
  background: var(--vscode-button-hoverBackground);
  color: var(--vscode-button-foreground);
  text-decoration: underline;
}

/* Code Preview Styling */
.code-preview-container {
  margin-top: 8px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background: var(--vscode-editor-background);
  max-height: 120px; /* Reduced height */
  overflow: hidden;
}

.code-preview-header {
  background: var(--vscode-titleBar-inactiveBackground);
  color: var(--vscode-foreground);
  padding: 3px 6px; /* Reduced padding */
  font-size: 10px; /* Smaller font */
  font-weight: 500;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.code-preview-content {
  max-height: 100px; /* Reduced height */
  overflow-y: auto;
  padding: 2px 0; /* Reduced padding */
  font-family: var(--vscode-editor-font-family);
  font-size: 10px; /* Smaller font */
  line-height: 1.2; /* Tighter line height */
}

.code-line {
  display: flex;
  align-items: flex-start;
  padding: 0;
  min-height: 12px; /* Reduced height */
}

.line-number {
  color: var(--vscode-editorLineNumber-foreground);
  background: var(--vscode-editorGutter-background);
  padding: 0 6px; /* Reduced padding */
  text-align: right;
  min-width: 30px; /* Reduced width */
  font-size: 9px; /* Smaller font */
  border-right: 1px solid var(--vscode-panel-border);
  user-select: none;
  flex-shrink: 0;
}

.line-content {
  color: var(--vscode-editor-foreground);
  padding: 0 6px; /* Reduced padding */
  white-space: pre;
  flex: 1;
  word-break: break-all;
  font-size: 10px; /* Smaller font */
}

/* Scrollbar styling for code preview */
.code-preview-content::-webkit-scrollbar {
  width: 8px;
}

.code-preview-content::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.code-preview-content::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
  border-radius: 4px;
}

.code-preview-content::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-activeBackground);
}

/* User Action Prompt Styles */
.user-action-prompt {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  padding: 16px;
  margin: 10px 0;
}

.compilation-status {
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 12px;
  font-weight: 500;
}

.compilation-status.success {
  background-color: var(
    --vscode-testing-iconPassed-background,
    rgba(0, 128, 0, 0.1)
  );
  color: var(--vscode-testing-iconPassed);
  border: 1px solid var(--vscode-testing-iconPassed);
}

.compilation-status.warning {
  background-color: var(
    --vscode-testing-iconQueued-background,
    rgba(255, 165, 0, 0.1)
  );
  color: var(--vscode-testing-iconQueued);
  border: 1px solid var(--vscode-testing-iconQueued);
}

.compilation-status.error {
  background-color: var(
    --vscode-testing-iconFailed-background,
    rgba(255, 0, 0, 0.1)
  );
  color: var(--vscode-testing-iconFailed);
  border: 1px solid var(--vscode-testing-iconFailed);
}

.action-message {
  color: var(--vscode-foreground);
  font-size: 13px;
  margin-bottom: 16px;
  line-height: 1.4;
}

.terminal-commands {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.terminal-command-item {
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.command-description {
  color: var(--vscode-foreground);
  font-size: 12px;
  font-weight: 500;
}

.command-detail {
  color: var(--vscode-descriptionForeground);
  font-size: 11px;
  font-family: var(--vscode-editor-font-family);
  background-color: var(--vscode-editor-background);
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid var(--vscode-panel-border);
}

.terminal-command-btn {
  align-self: flex-start;
  margin-top: 4px;
}
