/* Indexing screen styles */
.indexing-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 20px 0;
}

.indexing-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 30px;
    color: var(--vscode-foreground);
}

.indexing-description {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
    color: var(--vscode-descriptionForeground);
    max-width: 280px;
}

.privacy-note {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
    color: var(--vscode-descriptionForeground);
    max-width: 280px;
}

.learn-more {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 30px;
    display: inline-block;
}

.learn-more:hover {
    text-decoration: underline;
}

.workspace-display {
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 20px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    min-width: 200px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workspace-icon {
    font-size: 16px;
}

.github-option {
    margin: 15px 0;
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
}

.github-link {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
    cursor: pointer;
}

.github-link:hover {
    text-decoration: underline;
}

.github-input-container {
    margin-top: 15px;
    width: 100%;
    max-width: 280px;
}

.github-input-container input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 4px;
    font-size: 13px;
    box-sizing: border-box;
}

.btn-index {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 6px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-top: 20px;
    min-width: 150px;
}

.btn-index:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.btn-index:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.progress-container {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    background-color: var(--vscode-editor-selectionBackground);
    font-size: 11px;
}

.progress-step {
    margin: 5px 0;
    padding: 4px;
    border-radius: 2px;
}

.progress-step.active {
    background-color: var(--vscode-list-activeSelectionBackground);
}

.progress-step.completed {
    background-color: var(--vscode-testing-iconPassed);
    color: white;
}
