/* Animation and thinking state styles */
.message.thinking {
  background-color: var(--vscode-editor-background);
  border-left: 3px solid var(--vscode-charts-orange);
  opacity: 0.9;
}

.thinking-content {
  display: flex;
  align-items: center;
  gap: 10px;
  font-style: italic;
  color: var(--vscode-descriptionForeground);
  font-size: 13px;
}

.thinking-spinner {
  animation: pulse 1.5s ease-in-out infinite;
  font-size: 16px;
}

.animated-dots {
  display: inline-flex;
  margin-left: 2px;
}

.animated-dots .dot {
  animation: dotPulse 1.4s infinite ease-in-out;
  font-weight: bold;
  font-size: 16px;
}

.animated-dots .dot:nth-child(1) {
  animation-delay: 0s;
}
.animated-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}
.animated-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}

.thinking-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  background: transparent;
  border: none;
  margin-bottom: 16px;
  font-style: italic;
  color: var(--vscode-descriptionForeground);
}

.thinking-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-spinner {
  animation: pulse 1.5s ease-in-out infinite;
  font-size: 16px;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Loading dots animation for buttons */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.loading-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loadingDots {
  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Magnifying glass animation for impact analysis */
.magnifying-glass-animation {
  animation: magnifyingGlass 2s ease-in-out infinite;
  display: inline-block;
}

@keyframes magnifyingGlass {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) rotate(5deg);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.1) rotate(-3deg);
    opacity: 0.8;
  }
}
