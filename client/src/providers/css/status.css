/* Status and console output styles */
.console-output {
  margin: 2px 0;
  padding: 0;
  background: transparent;
  border: none;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  line-height: 1.4;
  text-align: left;
}

.console-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  opacity: 0.6;
  font-size: 11px;
}

.console-time {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.console-level {
  font-size: 11px;
  font-weight: bold;
  color: var(--vscode-descriptionForeground);
}

.console-message {
  color: var(--vscode-editor-foreground);
  margin: 0;
  padding: 0;
  text-align: left;
}

.status-message {
  margin: 2px 0;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  line-height: 1.3;
  text-align: left;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  color: var(--vscode-foreground);
  font-weight: 400;
  opacity: 0.9;
}

.summary-message {
  background: transparent !important;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 8px 0;
}

.summary-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.summary-title {
  font-weight: 600;
  font-size: 12px;
  color: var(--vscode-textLink-foreground);
}

.summary-content {
  color: var(--vscode-editor-foreground);
  line-height: 1.5;
  font-size: 11px;
}

.summary-content strong {
  color: var(--vscode-textLink-foreground);
  font-weight: 600;
}

.summary-content code {
  background-color: var(--vscode-textBlockQuote-background);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
}

.summary-details {
  margin-top: 12px;
  padding: 12px;
  background-color: var(--vscode-textBlockQuote-background);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-textLink-foreground);
  color: var(--vscode-editor-foreground);
  font-size: 13px;
  line-height: 1.6;
}

.summary-item {
  margin: 6px 0;
  padding-left: 4px;
  color: var(--vscode-editor-foreground);
  display: block;
  text-align: left;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
}

.summary-text {
  margin: 8px 0;
  color: var(--vscode-editor-foreground);
  font-weight: 500;
  display: block;
  text-align: left;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
}

.summary-details-clean {
  margin-top: 8px;
  padding: 0;
  background: transparent;
  border: none;
  color: var(--vscode-editor-foreground);
  font-size: 12px;
  line-height: 1.5;
  font-family: var(--vscode-font-family);
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-align: left;
  word-break: normal;
}

.summary-details-clean strong {
  color: var(--vscode-foreground);
  font-weight: 600;
}

.summary-details-clean code {
  background-color: var(--vscode-textCodeBlock-background);
  color: var(--vscode-textPreformat-foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
}

.status-icon {
  display: inline-block;
  margin-right: 8px;
  font-size: 12px;
  flex-shrink: 0;
  opacity: 0.7;
}

.status-content {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.status-text {
  font-weight: 400;
  color: var(--vscode-foreground);
  opacity: 0.9;
}

.status-details {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 4px;
}

/* Professional Error Message Styling */
.error-message {
  margin: 8px 0;
  padding: 0;
  background: transparent;
  border: none;
}

.error-content {
  padding: 12px;
  border-radius: 4px;
  background: var(--vscode-inputValidation-errorBackground);
  border-left: 3px solid var(--vscode-inputValidation-errorBorder);
}

.error-text {
  color: var(--vscode-inputValidation-errorForeground);
  font-size: 11px;
  line-height: 1.4;
  font-weight: 400;
  margin: 0 0 6px 0;
}

.retry-btn {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: var(--vscode-button-hoverBackground);
}

/* Activity Banner Styling */
.activity-banner {
  margin: 2px 0;
  padding: 4px 8px;
  background: #6c757d;
  border-radius: 3px;
  font-size: 10px;
  line-height: 1.2;
}

.activity-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.activity-text {
  color: white;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.activity-tick {
  color: white;
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
}

/* Professional Tasks Display */
.tasks-identified {
  margin: 12px 0;
  padding: 0;
  background: transparent;
  border: none;
}

.tasks-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.tasks-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.tasks-count {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item-professional {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  background: var(--vscode-textCodeBlock-background);
}

.task-number {
  background: var(--vscode-panel-border);
  color: var(--vscode-foreground);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.task-content {
  flex: 1;
  min-width: 0;
}

.task-description {
  font-size: 13px;
  color: var(--vscode-foreground);
  line-height: 1.4;
  margin-bottom: 4px;
}

.task-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.task-target {
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  color: var(--vscode-textPreformat-foreground);
  background: var(--vscode-textCodeBlock-background);
  padding: 2px 6px;
  border-radius: 3px;
}

.task-action {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

/* Professional Execution Plan Display */
.execution-plan {
  margin: 12px 0;
  padding: 0;
  background: transparent;
  border: none;
}

.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.plan-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.plan-count {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.plan-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.plan-step-professional {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  background: var(--vscode-textCodeBlock-background);
}

.step-number {
  background: var(--vscode-panel-border);
  color: var(--vscode-foreground);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  min-width: 0;
}

.step-description {
  font-size: 13px;
  color: var(--vscode-foreground);
  line-height: 1.4;
  margin-bottom: 4px;
}

.step-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.step-target {
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  color: var(--vscode-textPreformat-foreground);
  background: var(--vscode-textCodeBlock-background);
  padding: 2px 6px;
  border-radius: 3px;
}

.step-action {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.step-effort {
  color: var(--vscode-descriptionForeground);
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  background: var(--vscode-textCodeBlock-background);
}

/* File Operation Indicator */
.file-operation-indicator {
  margin: 4px 0;
  padding: 8px 12px;
  background: #2d2d2d;
  border-radius: 6px;
  border-left: 3px solid #007acc;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
  line-height: 1.4;
}

.file-operation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.file-operation-action {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 500;
}

.file-operation-icon {
  font-size: 14px;
  color: #007acc;
}

.file-operation-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.file-operation-status.in-progress {
  color: #ffa500;
}

.file-operation-status.success {
  color: #28a745;
}

.file-operation-status.error {
  color: #dc3545;
}

.file-operation-path {
  color: #9cdcfe;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  opacity: 0.8;
  word-break: break-all;
}

.file-operation-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid #ffa500;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Clean Tasks Display */
.tasks-header-collapsible {
  cursor: pointer;
  padding: 8px 0;
  border-bottom: 1px solid #3c3c3c;
  margin-bottom: 8px;
}

.tasks-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tasks-title-row h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.tasks-count {
  background: #4a4a4a;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.collapse-indicator {
  font-size: 12px;
  color: #888888;
  transition: transform 0.2s ease;
}

.tasks-list.collapsed {
  display: none;
}

.tasks-header-collapsible:hover .collapse-indicator {
  transform: rotate(-90deg);
}

.task-item-clean {
  display: flex;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #2a2a2a;
  text-align: left;
}

.task-item-clean:last-child {
  border-bottom: none;
}

.task-number {
  background: #4a4a4a;
  color: #ffffff;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.task-description-clean {
  font-size: 12px;
  line-height: 1.4;
  color: #cccccc;
  text-align: left;
  word-wrap: break-word;
  flex: 1;
}

/* Clean Terminal Commands */
.operation-item-clean {
  margin: 12px 0;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 6px;
  border-left: 3px solid #007acc;
}

.operation-header-clean {
  margin-bottom: 8px;
}

.operation-title-clean {
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
  text-align: left;
}

.operation-description-clean {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 8px;
  text-align: left;
}

.terminal-commands-clean {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.terminal-command-box-clean {
  display: flex;
  align-items: center;
  background: #1e1e1e;
  border: 1px solid #3c3c3c;
  border-radius: 4px;
  padding: 8px 12px;
  gap: 12px;
}

.terminal-code-clean {
  flex: 1;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  font-size: 12px;
  color: #d4d4d4;
  background: transparent;
  border: none;
  text-align: left;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.play-button-clean {
  background: #007acc;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.play-button-clean:hover {
  background: #005a9e;
}

.play-button-clean:active {
  background: #004578;
}

/* Clean Action Buttons */
.approve-btn-clean,
.reject-btn-clean {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  margin: 8px 4px 0 0;
  transition: background-color 0.2s ease;
}

.approve-btn-clean {
  background: #28a745;
  color: white;
}

.approve-btn-clean:hover {
  background: #218838;
}

.reject-btn-clean {
  background: #dc3545;
  color: white;
}

.reject-btn-clean:hover {
  background: #c82333;
}

/* Action-based UI Components */
.action-indicator {
  display: flex;
  align-items: center;
  background: #2a2a2a;
  border-left: 3px solid #007acc;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 6px 0;
  font-size: 11px;
  color: #cccccc;
  transition: background-color 0.2s ease;
}

.action-indicator:hover {
  background: #333333;
}

.action-icon {
  margin-right: 8px;
  font-size: 12px;
  color: #007acc;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.action-type {
  font-weight: 600;
  color: #ffffff;
  font-size: 11px;
}

.action-file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #9cdcfe;
  font-size: 10px;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
}

.action-file-name {
  font-weight: 500;
  color: #d4d4d4;
}

.action-file-path {
  opacity: 0.7;
  color: #9cdcfe;
}

.action-file-link {
  background: #007acc;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 9px;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.action-file-link:hover {
  background: #005a9e;
  text-decoration: none;
  color: white;
}

.action-status {
  margin-left: 8px;
  flex-shrink: 0;
}

.action-status.in-progress {
  color: #ffa500;
}

.action-status.success {
  color: #28a745;
}

.action-status.error {
  color: #dc3545;
}
