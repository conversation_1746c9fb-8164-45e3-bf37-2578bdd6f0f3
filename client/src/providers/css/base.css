/* Base styles for the sidebar provider */
body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    background-color: var(--vscode-sideBar-background);
    margin: 0;
    padding: 20px;
    overflow-x: hidden;
    text-align: center;
}

.settings-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    color: var(--vscode-icon-foreground);
}

.settings-icon:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.hidden {
    display: none !important;
}

.settings-panel {
    position: absolute;
    top: 40px;
    right: 10px;
    background-color: var(--vscode-dropdown-background);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 4px;
    padding: 10px;
    min-width: 200px;
    z-index: 1000;
    font-size: 12px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-size: 11px;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 4px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 2px;
    font-size: 11px;
    box-sizing: border-box;
}
