import axios, { AxiosRequestConfig } from "axios";

// Base API configuration for AI Coding Assistant extension
// Build default URL from environment variables
const host = process.env.RECODE_SERVER_HOST || "127.0.0.1";
const port = process.env.RECODE_SERVER_PORT || "8000";
const DEFAULT_BASE_URL = `http://${host}:${port}`;

class ApiService {
  private baseURL: string;
  private apiKey: string = "";

  constructor(baseURL: string = DEFAULT_BASE_URL) {
    this.baseURL = baseURL;
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  setBaseURL(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getHeaders() {
    return {
      "Content-Type": "application/json",
      ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
    };
  }

  async get(endpoint: string, config?: AxiosRequestConfig) {
    try {
      const response = await axios.get(`${this.baseURL}${endpoint}`, {
        ...config,
        headers: { ...this.getHeaders(), ...config?.headers },
      });
      return response.data;
    } catch (error) {
      console.error("API GET error:", error);
      throw error;
    }
  }

  async post(endpoint: string, data?: any, config?: AxiosRequestConfig) {
    try {
      const response = await axios.post(`${this.baseURL}${endpoint}`, data, {
        ...config,
        headers: { ...this.getHeaders(), ...config?.headers },
      });
      return response.data;
    } catch (error) {
      console.error("API POST error:", error);
      throw error;
    }
  }

  async put(endpoint: string, data?: any, config?: AxiosRequestConfig) {
    try {
      const response = await axios.put(`${this.baseURL}${endpoint}`, data, {
        ...config,
        headers: { ...this.getHeaders(), ...config?.headers },
      });
      return response.data;
    } catch (error) {
      console.error("API PUT error:", error);
      throw error;
    }
  }

  async delete(endpoint: string, config?: AxiosRequestConfig) {
    try {
      const response = await axios.delete(`${this.baseURL}${endpoint}`, {
        ...config,
        headers: { ...this.getHeaders(), ...config?.headers },
      });
      return response.data;
    } catch (error) {
      console.error("API DELETE error:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
