# ReCode - AI-Powered Code Modernization Extension

ReCode is a VSCode extension designed to help developers modernize legacy code and interact with their codebase through AI-powered agents. The extension provides an intuitive interface for indexing codebases, asking questions, and generating code improvements through a multi-agent system.

## Features

### 🔧 **Settings & Configuration**

- Clean, organized settings panel accessible via VSCode's extension settings
- LLM Provider Selection: Support for Azure OpenAI and Google Gemini
- Secure API key management with connection testing
- Visual connection status indicators

### 📚 **Codebase Indexing**

- Index your current VSCode workspace
- Index GitHub repositories via URL
- Real-time progress tracking with visual indicators
- Vector database storage for efficient code search

### 💬 **Ask Mode - Interactive Q&A**

- Chat-style interface for asking questions about your codebase
- AI-powered responses with relevant code snippets
- Clickable code references that navigate to actual files
- Persistent conversation history during sessions

### 🚀 **Code Mode - Generation & Improvement**

- Large text area for detailed code requests
- Real-time progress tracking through multi-agent workflow
- Code generation with error handling and best practices
- Automated code review and suggestions

## Installation & Setup

### Prerequisites

- VSCode 1.74.0 or higher
- Node.js (v22 or higher)
- Valid API key for your chosen LLM provider

### Development Setup

1. **Navigate to the client directory**

   ```bash
   cd ReCodeAI/client
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Compile the extension**

   ```bash
   npm run compile
   ```

4. **Watch for changes during development**
   ```bash
   npm run watch
   ```
