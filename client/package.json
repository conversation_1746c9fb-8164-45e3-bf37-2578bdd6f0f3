{"name": "ai-coding-assistant", "displayName": "AI Coding Assistant", "description": "AI-powered code modernization and analysis extension", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onView:recode-sidebar"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "recode.indexWorkspace", "title": "Index Current Workspace", "category": "AI Assistant"}, {"command": "recode.fullIndex", "title": "Full Index - Index All Files", "category": "AI Assistant"}, {"command": "recode.differentialSync", "title": "Differential Sync - Sync Only Changed Files", "category": "AI Assistant"}], "viewsContainers": {"activitybar": [{"id": "recode-explorer", "title": "AI Assistant", "icon": "$(code)"}]}, "views": {"recode-explorer": [{"id": "recode-sidebar", "type": "webview", "name": "AI Assistant", "when": "true"}]}, "configuration": {"title": "AI Assistant", "properties": {"recode.llmProvider": {"type": "string", "default": "azure-openai", "enum": ["azure-openai", "google-gemini"], "description": "LLM Provider to use"}, "recode.llmModel": {"type": "string", "default": "gpt-4", "description": "LLM Model to use"}, "recode.apiKey": {"type": "string", "default": "", "description": "API Key for the LLM provider"}, "recode.isIndexed": {"type": "boolean", "default": false, "description": "Whether the current workspace has been indexed"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/node": "^22.0.0", "@types/pako": "^2.0.0", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"@types/ws": "^8.18.1", "axios": "^1.7.2", "ignore": "^5.3.0", "langchain": "^0.2.0", "pako": "^2.1.0", "ws": "^8.18.2", "uuid": "^9.0.0"}}