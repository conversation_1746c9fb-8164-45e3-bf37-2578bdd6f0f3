{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM"], "sourceMap": true, "rootDir": "src", "strict": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "types": ["vscode", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out"]}